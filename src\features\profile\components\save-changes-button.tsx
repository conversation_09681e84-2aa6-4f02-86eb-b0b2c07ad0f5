import { Button } from '@/shared/components/ui/button'
import { cn } from '@/shared/lib/utils'
import { SaveIcon } from 'lucide-react'
import React from 'react'
import { useFormContext } from 'react-hook-form'

interface SaveChangesButtonProps {
    className?: string
}

/**
 * Save Changes Button.
 * This is generic button for profile forms,
 * Eg: ProfileInfoForm, ProfessionalInfoForm and ContactInfoForm.
 */
function SaveChangesButton({ className }: SaveChangesButtonProps) {
    const form = useFormContext()
    const isDirty = form.formState.isDirty
    const isSubmitting = form.formState.isSubmitting

    return (
        <Button
            type='submit'
            className={cn("h-10 text-sm", className)}
            variant={"outline-primary"}
            disabled={!isDirty || isSubmitting}
            isLoading={isSubmitting}
        >
            <SaveIcon />
            Save Changes
        </Button>
    )
}

export default SaveChangesButton