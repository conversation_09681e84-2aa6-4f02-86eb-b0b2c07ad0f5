"use client"

import { createContext, useContext } from "react"
import { ApiIndustryOption } from "../types/profile-roles.type"

interface ProfileSelectOptionsContextType {
    industryOptions: ApiIndustryOption[]
}

export const ProfileSelectOptionsContext = createContext<ProfileSelectOptionsContextType | null>(null)

export function ProfileSelectOptionsContextProvider(props: { children: React.ReactNode, industryOptions: ApiIndustryOption[] }) {
    const { children, industryOptions } = props

    return (
        <ProfileSelectOptionsContext.Provider value={{ industryOptions }}>
            {children}
        </ProfileSelectOptionsContext.Provider>
    )
}

export function useProfileSelectOptions() {
    const context = useContext(ProfileSelectOptionsContext)
    if (!context) {
        throw new Error("useProfileSelectOptions must be used within a ProfileSelectOptionsContextProvider")
    }
    return context
}