import {
    LayoutDashboard,
    LayoutGrid,
    Cog,
    Calendar,
    Folder,
    Cpu,
    UserPlus,
    MessageCircle,
    Star,
    FileText,
    Monitor,
    TrendingUp,
    User,
    Users,
    CreditCard,
    File,
    DollarSign,
    HelpCircle,
    HomeIcon,
} from "lucide-react"
import { PAGE_ROUTES } from "./page-routes"
import { Icon } from "@/shared/types"


export type SidebarLink = {
    label: string
    subLinks: {
        label: string
        href: string
        Icon: Icon
    }[],
}


export const SIDEBAR_LINKS: SidebarLink[] = [
    {
        label: "Dashboard",
        subLinks: [
            {
                href: PAGE_ROUTES.dashboard,
                Icon: LayoutDashboard,
                label: "Dashboard",
            },
        ],
    },
    // Listings group
    {
        label: "Listings",
        subLinks: [
            {
                href: PAGE_ROUTES.properties,
                Icon: HomeIcon,
                label: "Properties",
            },
            {
                label: "Projects",
                href: PAGE_ROUTES.listings.projects,
                Icon: LayoutGrid,
            },
            {
                label: "Services",
                href: PAGE_ROUTES.listings.services,
                Icon: Cog,
            },  
            {   
                label: "Events",
                href: PAGE_ROUTES.listings.events,
                Icon: Calendar,
            },
            {
                label: "Portfolio",
                href: PAGE_ROUTES.listings.portfolio,
                Icon: Folder,
            },
            {
                label: "AI Agents",
                href: PAGE_ROUTES.listings.aiAgents,
                Icon: Cpu,
            },
        ]
    },
    // CRM group
    {
        label: "CRM",
        subLinks: [
            {
                label: "Leads",
                href: PAGE_ROUTES.crm.leads,
                Icon: UserPlus,
            },
            {
                label: "Chats",
                href: PAGE_ROUTES.crm.chats,
                Icon: MessageCircle,
            },
            {
                label: "Reviews",
                href: PAGE_ROUTES.crm.reviews,
                Icon: Star,
            },
            {
                label: "Invoices",
                href: PAGE_ROUTES.crm.invoices,
                Icon: FileText,
            },
        ]
    },

    // Ads Manager group
    {
        label: "Ads Manager",
        subLinks: [

            {
                label: "Ads Manager",
                href: PAGE_ROUTES.adsManager.adsManager,
                Icon: Monitor,
            },
            {
                label: "Ads Performance",
                href: PAGE_ROUTES.adsManager.adsPerformance,
                Icon: TrendingUp,
            },
        ]
    },

    // Business Management group
    {
        label: "Business Management",
        subLinks: [
            {
                label: "Profile",
                href: PAGE_ROUTES.profile,
                Icon: User,
            },

            {
                label: "Team",
                href: PAGE_ROUTES.businessManagement.team,
                Icon: Users,
            },

            {
                label: "Subscription",
                href: PAGE_ROUTES.businessManagement.subscription,
                Icon: CreditCard,
            },

            {
                label: "Documents",
                href: PAGE_ROUTES.businessManagement.documents,
                Icon: File,
            },

            {
                label: "Payments",
                href: PAGE_ROUTES.businessManagement.payments,
                Icon: DollarSign,
            },
        ]
    },
    
    // Help
    {
        label: "Help",
        subLinks: [
            {
                label: "Help Center",
                href: PAGE_ROUTES.help.help,
                Icon: HelpCircle,
            },
        ]   
    },
]