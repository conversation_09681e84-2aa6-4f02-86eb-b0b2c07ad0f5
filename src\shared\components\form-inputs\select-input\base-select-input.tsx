import React from 'react'
import FormGroup from '../form-group'
import {  FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import { Select, SelectTrigger, SelectValue } from '@/ui/select'
import { ControllerFieldState, ControllerRenderProps, FieldPath, FieldValues } from 'react-hook-form'
import { useFormField } from '@/ui/form'
import { cn } from '@/lib/utils'
import { SelectContent, SelectItem } from '@/ui/select'
import { SelectOption, SelectOptions } from '@/shared/types'

type BaseSelectInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = FormGroupPropsWithoutChildrenAndIcons & {
    field: ControllerRenderProps<TFieldValues, TName>,
    fieldState: ControllerFieldState,
    placeholder?: string,
    options: SelectOptions,
    className?: string,
    disabled?: boolean
}

function BaseSelectInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    field,
    fieldState,
    placeholder,
    options,
    className,
    disabled,
    ...props
}: BaseSelectInputProps<TFieldValues, TName>) {
    const { error } = useFormField()
    const {
        onChange,
        value,
        name,
        ...restFieldProps
    } = field
    return (
        <FormGroup {...props}>
            <Select
                onValueChange={onChange}
                defaultValue={value}
                value={value || ""}
                name={name}
                disabled={disabled}
            >
                <SelectTrigger
                    className={cn('h-11 w-full', className)}
                    aria-invalid={!!error}
                    {...restFieldProps}
                    ref={restFieldProps.ref}
                >
                    <SelectValue
                        placeholder={placeholder}
                    />
                </SelectTrigger>
                <SelectContent>
                    {options.map((option) => (
                        <SelectItem 
                            key={option.value} 
                            value={option.value}
                            className='cursor-pointer'
                        >
                            {option.label}
                        </SelectItem>
                    ))} 
                </SelectContent>
            </Select>
        </FormGroup>
    )
}

export default BaseSelectInput