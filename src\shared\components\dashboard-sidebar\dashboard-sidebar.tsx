"use client"

import React from 'react'
import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarGroupLabel,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuItem,
    SidebarMenuButton,
    SidebarTrigger
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown } from 'lucide-react'
import Logo from '@/components/logo'
import { SIDEBAR_LINKS } from '@/constants/sidebar-links'
import Link from 'next/link'
import DashboardSidebarLink from './dashboard-sidebar-link'

function DashboardSidebar() {
    return (
        <Sidebar>
            <SidebarHeader className='p-0 h-nav border-b flex flex-row items-center justify-between px-dashboard-layout'>
                <Logo />
            </SidebarHeader>
            <SidebarContent className='p-0'>
                <SidebarMenu>
                    {SIDEBAR_LINKS.map((link) => (
                        <SidebarGroup key={link.label}>
                            {link.label === "Dashboard" ? (
                                <>
                                    <SidebarGroupLabel>
                                        {link.label}
                                    </SidebarGroupLabel>
                                    <SidebarGroupContent>
                                        <SidebarMenu>
                                            {link.subLinks.map((subLink) => (
                                                <DashboardSidebarLink key={subLink.label} {...subLink} />
                                            ))}
                                        </SidebarMenu>
                                    </SidebarGroupContent>
                                </>
                            ) : (
                                <Collapsible defaultOpen className="group/collapsible">
                                    <SidebarGroupLabel asChild>
                                        <CollapsibleTrigger className="justify-between w-full">
                                            {link.label}
                                            <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                                        </CollapsibleTrigger>
                                    </SidebarGroupLabel>
                                    <CollapsibleContent>
                                        <SidebarGroupContent>
                                            <SidebarMenu>
                                                {link.subLinks.map((subLink) => (
                                                    <DashboardSidebarLink 
                                                        key={subLink.label} 
                                                        {...subLink} 
                                                    />
                                                ))}
                                            </SidebarMenu>
                                        </SidebarGroupContent>
                                    </CollapsibleContent>
                                </Collapsible>
                            )}
                        </SidebarGroup>
                    ))}
                </SidebarMenu>
            </SidebarContent>
        </Sidebar>
    )
}

export default DashboardSidebar