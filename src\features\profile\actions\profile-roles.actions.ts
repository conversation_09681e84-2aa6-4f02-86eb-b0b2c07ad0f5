"use server"

import API_ENDPOINTS from "@/shared/constants/api-routes"
import { executeApiRequest } from "@/lib/helpers/actions.helpers"
import { REQUEST_TYPE } from "@/shared/types/action-helper.type"
import { ApiResponse } from "@/shared/types/utility.type"
import { Nationality } from "@/profile/types/nationality.type"
import { Location } from "../types/location.type"
import { ApiIndustryOption, ApiIndustryRoleOption } from "../types/profile-roles.type"


/**
 * Get all nationalities from the API
 * @returns ApiResponse<Nationality[]>
 */
export async function getNationalitiesOptions(): Promise<ApiResponse<Nationality[]>> {
    const response = await executeApiRequest<Nationality[]>({
        method: REQUEST_TYPE.get,
        url: API_ENDPOINTS.GET_NATIONALITY,
        options: {
            cache: "force-cache",
        }
    })
    return response
}


/*
 * Get all locations options from the API
 * @returns ApiResponse<Location[]>
 */
export async function getLocationsOptions(): Promise<ApiResponse<Location[]>> {
    const response = await executeApiRequest<Location[]>({
        method: REQUEST_TYPE.get,
        url: API_ENDPOINTS.ALL_LOCATIONS,
        options: {
            cache: "force-cache",
        }
    })
    return response
}

/**
 * Get all industries options from the API
 * @returns ApiResponse<ApiIndustryOption[]>
 */
export async function getIndustriesOptions(): Promise<ApiResponse<ApiIndustryOption[]>> {
    const response = await executeApiRequest<ApiIndustryOption[]>({
        method: REQUEST_TYPE.get,
        url: API_ENDPOINTS.ALL_PARENT_SERVICES,
        options: {
            cache: "force-cache",
        }
    })
    return response
}

export async function getIndustryRolesOptions(industryId: number): Promise<ApiResponse<ApiIndustryRoleOption[]>> {
    const response = await executeApiRequest<ApiIndustryRoleOption[]>({
        method: REQUEST_TYPE.get,
        url: `${API_ENDPOINTS.ALL_PARENT_SERVICES}/${industryId}/agent`,
        options: {
            cache: "force-cache",
            next: {
                tags: ["industry-roles"],
            }
        }
    })
    return response
}