"use client"

import React from 'react'
import { useFormContext } from 'react-hook-form'
import { RegistrationFormSchema } from '@/auth/schemas/forms/registration-form.schema'
import { TextInput } from '@/form-inputs/text-input'
import { Button } from '@/ui/button'
import { useRegistrationFormSteps } from '@/auth/context/registration-form-steps-context'
import AuthCardWrapper from '@/auth/components/auth-card-wrapper'

/**
 * Step Two of the Registration Form
 * This is the form where the user can enter their email, password and confirm password
 */
function StepTwoForm() {
    const { goToPreviousStep } = useRegistrationFormSteps()
    const { 
        control, 
        formState: { isSubmitting } 
    } = useFormContext<RegistrationFormSchema>()

    return (
        <AuthCardWrapper 
            title="Account Setup" 
            backButtonCallback={goToPreviousStep}
        >
            <div className='flex flex-col gap-5'>
                <TextInput
                    label='Email'
                    control={control}
                    name="email"
                    type='email'
                />
                <TextInput
                    label='Password'
                    control={control}
                    name="password"
                    type='password'
                />
                <TextInput
                    label='Confirm Password'
                    control={control}
                    name="confirmPassword"
                    type='password'
                />
                <Button
                    type='submit'
                    className='mt-2'
                    disabled={isSubmitting}
                    isLoading={isSubmitting}
                >
                    {isSubmitting ? "Creating Account" : "Create Account"}
                </Button>
            </div>
        </AuthCardWrapper>
    )
}

export default StepTwoForm