import OtpVerificationForm, { OtpVerificationFormProps } from '@/auth/components/forms/otp-verification-form'
import OtpVerificationText from '@/auth/components/otp-verification-text'
import { cn } from '@/lib/utils'
import React from 'react'

interface OtpVerificationLayoutProps {
    /**
     * Title of the page
     */
    header: string,

    /**
     * Class name for the header
     */
    headerClassName?: string,

    /**
     * OTP Verification Form Props
     */
    otpVerificationFormProps: OtpVerificationFormProps,

}

function OtpVerificationLayout({
    header,
    headerClassName,
    otpVerificationFormProps,
}: OtpVerificationLayoutProps) {
    return (
        <div className='flex flex-col gap-7 w-full items-center'>
            <div className="flex flex-col gap-2">
                <h1 
                    className={cn(
                        'text-2xl font-bold  text-center font-heading', 
                        headerClassName
                    )}>
                    {header}
                </h1>
                <OtpVerificationText />
            </div>
            <OtpVerificationForm {...otpVerificationFormProps} />
        </div>
    )
}

export default OtpVerificationLayout