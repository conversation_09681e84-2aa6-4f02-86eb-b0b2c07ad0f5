import { ApiResponse, ErrorApiResponse, SuccessApiResponse } from "@/shared/types/utility.type"
import { errorToast, successToast } from "@/lib/toast"

/**
 * The properties for handling the form submission.
 * @param onSuccess - The function to call when the form submission is successful.
 * @param onError - The function to call when the form submission is unsuccessful.
 * @param successMessage - The message to display when the form submission is successful.
 * @param errorMessage - The message to display when the form submission is unsuccessful.
 * @param response - The response from the API.
 * @param hideDefaultErrorToast - Whether to hide the default error toast.
 * @param hideDefaultSuccessToast - Whether to hide the default success toast.
 */
interface HandleApiResponseProps {
    /**
     * The function to call when the form submission is successful.
     */
    onSuccess?: () => void
    /**
     * The function to call when the form submission is unsuccessful.
     */
    onError?: (error: ErrorApiResponse) => void
    /**
     * The message to display when the form submission is successful.
     */
    successMessage?: string
    /**
     * The response from the API.
     */
    response: ApiResponse
    /**
     * Whether to hide the default error toast.
     */
    hideDefaultErrorToast?: boolean
    /**
     * Whether to hide the default success toast.
     */
    hideDefaultSuccessToast?: boolean
    /**
     * The message to display when the form submission is unsuccessful.
     */
    errorMessage?: string

    /**
     * The function to call whether API response is successful or not.
     */
    finallyHandler?: () => void
}

/**
 * Handles the API response on client side after form submission and provides a consistent way to handle success and error responses.
 * @param onSuccess - The function to call when the form submission is successful.
 * @param onError - The function to call when the form submission is unsuccessful.
 * @param successMessage - The message to display when the form submission is successful.
 * @param errorMessage - The message to display when the form submission is unsuccessful.
 * @param response - The response from the API.
 * @returns void
 */
export function handleApiResponse({
    onSuccess,
    onError,
    successMessage,
    errorMessage,
    response,
    hideDefaultErrorToast,
    hideDefaultSuccessToast,
    finallyHandler
}: HandleApiResponseProps) {
    try {
        if (!response.success) {
            if (onError) {
                onError(response)
            }
            throw new Error(response.message)
        }
        if (onSuccess) {
            onSuccess()
        }
        if (!hideDefaultSuccessToast) {
            successToast(successMessage || response.message)
        }

    } catch (error) {
        if (!hideDefaultErrorToast) {
            errorToast(errorMessage || response.message)
        }
        
    } finally {
        finallyHandler?.()
    }
}
