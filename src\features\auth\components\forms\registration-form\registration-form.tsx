"use client"

import React, { useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { registerAgent } from '@/auth/actions/auth.action'
import { USER_TEMP_EMAIL } from '@/constants/local-storage-keys'
import { useRouter } from 'next/navigation'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { handleApiResponse } from '@/lib/helpers/client.helpers'
import AuthRedirectText from '@/auth/components/auth-redirect-text'
import RegistrationFormSteps from './registration-form-steps'
import { CLIENT_SUCCESS_MESSAGES } from '@/constants/messages/client'
import { 
    RegistrationFormSchema, 
    registrationFormSchema 
} from '@/auth/schemas/forms/registration-form.schema'


/**
 * Registration Form
 * This is the main form for the registration process.
 * This form has two steps,
 * 1. Step One: Where the user selects their account type
 * 2. Step Two: Where the user enters their email, password and confirm password
 * It handles the form validation and submission.
 */
function RegistrationForm() {

    const router = useRouter()

    // Initialize the form
    const form = useForm<RegistrationFormSchema>({
        resolver: zodResolver(registrationFormSchema),
        defaultValues: {
            accountType: "",
            email: "",
            password: "",
            confirmPassword: "",
        },
    })

    // Handle the form submission
    const onSubmit = async (data: RegistrationFormSchema) => {
        const res = await registerAgent(data)
        handleApiResponse({
            response: res,
            successMessage: CLIENT_SUCCESS_MESSAGES.agentRegisteredSuccess,
            onSuccess: () => {
                localStorage.setItem(USER_TEMP_EMAIL, data.email)
                router.push(PAGE_ROUTES.otpVerification)
            },
        })
    }

    // Prefetch the OTP verification route so it's ready by the time we navigate to it
    useEffect(() => {
        router.prefetch(PAGE_ROUTES.otpVerification)
    }, [router])

    return (
        <FormProvider {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='w-full'
                noValidate
            >
                <RegistrationFormSteps />
                <AuthRedirectText
                    prompt="Already have an account?"
                    linkText="Login"
                    href={PAGE_ROUTES.login}
                    className='mt-6'
                />
            </form>
        </FormProvider>
    )
}


export default RegistrationForm