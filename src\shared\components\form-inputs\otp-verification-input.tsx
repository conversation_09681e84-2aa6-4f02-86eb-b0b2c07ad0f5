"use client"

import {
    InputOTP,
    InputOTPGroup,
    InputOTPSlot,
} from "@/components/ui/input-otp"
import { REGEXP_ONLY_DIGITS } from "input-otp"
import React, { ComponentProps } from 'react'
import {
    Control,
    FieldPath,
    FieldValues
} from 'react-hook-form'
import { FormField } from "../ui/form"

type OtpVerificationInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
    control: Control<TFieldValues>,
    name: TName,
    inputProps?: ComponentProps<typeof InputOTP>,
}

function OtpVerificationInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({ control, name, inputProps }: OtpVerificationInputProps<TFieldValues, TName>) {

    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <InputOTP
                    maxLength={4}
                    pattern={REGEXP_ONLY_DIGITS}
                    {...field}
                    
                >
                    <InputOTPGroup >
                        <InputOTPSlot index={0} className="w-12 h-12" />
                        <InputOTPSlot index={1} className="w-12 h-12" slot="input-otp-slot" />
                        <InputOTPSlot index={2} className="w-12 h-12" slot="input-otp-slot" />
                        <InputOTPSlot index={3} className="w-12 h-12" slot="input-otp-slot" />
                    </InputOTPGroup>
                </InputOTP>
            )}
        />

    )
}

export default OtpVerificationInput