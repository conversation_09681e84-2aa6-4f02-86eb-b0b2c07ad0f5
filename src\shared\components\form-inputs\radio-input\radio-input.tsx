import React from 'react'
import { FormField } from '@/ui/form'
import { FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { SelectOptions } from '@/shared/types'
import FormGroup from '../form-group'
import BaseRadioInput from './base-radio-input'
import { cn } from '@/shared/lib/utils'

export interface RadioInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends FormGroupPropsWithoutChildrenAndIcons {
    control: Control<TFieldValues>
    name: TName
    options: SelectOptions
}

function RadioInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    control,
    name,
    options,
    ...formGroupProps
}: RadioInputProps<TFieldValues, TName>) {
    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormGroup 
                    {...formGroupProps}
                    labelClassName={cn(
                        'text-base font-normal text-foreground',
                        formGroupProps.labelClassName,
                    )}
                    errorMessageClassName='pt-2'
                >
                    <BaseRadioInput
                        options={options}
                        {...field}
                    />
                </FormGroup>
            )}
        />
    )
}

export default RadioInput