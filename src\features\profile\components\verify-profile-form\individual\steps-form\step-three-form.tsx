"use client"

import { Control, useFormContext } from 'react-hook-form'
import { useCompleteProfileFormOptions } from '@/profile/context/complete-profile-form-options-context'
import { parseIndustryOption, stringifyIndustryRoleOption } from '@/features/profile/helpers/options.helpers'
import { SelectInput } from '@/shared/components/form-inputs/select-input'
import { TextInput } from '@/shared/components/form-inputs/text-input'
import { SELECT_OPTION_IDS } from '@/constants/select-option-ids'
import { Step3FormType, step3Schema } from '@/features/profile/schemas/profile-registration/individual.schema'
import useIndividualRegistrationFormStore from '@/shared/context/individual-registration-form-store'
import { useFormSteps } from '@/shared/context/form-steps-context'
import NavigationButton from '../navigation-button'
import BaseForm from '@/shared/components/base-form'
import CheckboxCard from '../../../checkbox-card'


function StepThreeForm() {

    const { updateStep, form: mainForm } = useIndividualRegistrationFormStore()
    const { goToNextStep } = useFormSteps()

    const primaryIndustries = mainForm.step2.primaryIndustries
    const otherIndustries = mainForm.step2.otherPrimaryIndustry

    const onSubmit = (data: Step3FormType) => {
        // Filtering the data to only include the industries that are in the primaryIndustries
        const defaultIndustryRoles = Object.entries(data.defaultIndustryRoles)
            .filter(([key]) => primaryIndustries.includes(key))
            .reduce((acc, [key, value]) => {
                acc[key] = value
                return acc
            }, {} as Record<string, string>)
        
        
        // Filtering the default industry other roles to only include the industries
        //  that are in the primaryIndustries and the default industry role is other
        const defaultIndustryOtherRoles = Object.entries(data.defaultIndustryOtherRoles || {})
            .filter(([key]) => primaryIndustries.includes(key) && defaultIndustryRoles[key] === "other")
            .reduce((acc, [key, value]) => {
                acc[key] = value
                return acc
            }, {} as Record<string, string>)
        
        // Filtering the other industry roles to only include the industries that are in the otherIndustries
        const otherIndustryRoles = Object.entries(data.otherIndustryRoles || {})
            .filter(([key]) => otherIndustries.includes(key))
            .reduce((acc, [key, value]) => {
                acc[key] = value
                return acc
            }, {} as Record<string, string>)

        // Filtering the data to only include the industries that are in the primaryIndustries and otherIndustries
        const filteredData: Step3FormType = {
            defaultIndustryRoles,
            defaultIndustryOtherRoles,
            otherIndustryRoles,
        }

        updateStep('step3', filteredData)
        goToNextStep()
    }

    return (
        <BaseForm
            schema={step3Schema}
            onSubmit={onSubmit}
            defaultValues={mainForm.step3}
        >
            {({ control }) => {
                return (
                    <div className='flex flex-col gap-8'>
                        {primaryIndustries
                            .filter(industry => parseIndustryOption(industry).id !== SELECT_OPTION_IDS.OTHER_PRIMARY_INDUSTRY)
                            .map(industry => (
                                <PrimaryIndustryRoleSelectInput
                                    key={`${industry}-select-input-key`}
                                    industry={industry}
                                    control={control}
                                />
                            ))}
                        {otherIndustries.map((industry) => {
                            return (
                                <CheckboxCard key={`${industry}-checkbox-card-key`}>
                                    <TextInput
                                        key={`other-${industry}-text-input-key`}
                                        name={`otherIndustryRoles.${industry}`}
                                        label={industry}
                                        control={control}
                                        placeholder='Enter other role'
                                    />
                                </CheckboxCard>
                            )
                        })}
                        <NavigationButton />
                    </div>
                )
            }}
        </BaseForm>
    )
}

interface PrimaryIndustryRoleSelectInputProps {
    /**
     * Industry Option value in string format
     */
    industry: string
    control: Control<Step3FormType>
}

const PrimaryIndustryRoleSelectInput = ({
    industry,
    control
}: PrimaryIndustryRoleSelectInputProps) => {

    const { roles } = useCompleteProfileFormOptions()
    const formContext = useFormContext<Step3FormType>()
    const name = `defaultIndustryRoles.${industry}` as const
    const selectedRole = formContext.watch(name)
    const isOtherRole = selectedRole === "other"
    const industryOption = parseIndustryOption(industry)
    const industryLabel = industryOption.name
    const industryId = industryOption.id.toString()

    return (
        <CheckboxCard>
            <SelectInput
                name={name}
                placeholder='Select role'
                label={industryLabel}
                control={control}
                options={[
                    ...roles[industryId].map(role => ({
                        label: role.name,
                        value: stringifyIndustryRoleOption(role),
                    })),
                    {
                        label: "Other",
                        value: "other",
                    }
                ]}
            />
            {isOtherRole && (
                <TextInput
                    inputGroupClassName='pt-4'
                    name={`defaultIndustryOtherRoles.${industry}`}
                    label={`Other role (${industryLabel})`}
                    control={control}
                />
            )}
        </CheckboxCard>
    )

}


export default StepThreeForm 