// src/components/typog/Typography.tsx
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import * as React from "react"

/**
 * ------------------------------------------------------------------
 * Variants definition
 * ------------------------------------------------------------------
 *
 * Most devs only need the `variant` prop; other variants are optional
 * mix‑ins (size overrides, color overrides etc.).
 */
export const typographyVariants = cva("", {
    variants: {
        /** The semantic + visual intent bundle */
        variant: {
            /** Headings ************************************************************/
            h1: "scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
            h2: "scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0",
            h3: "scroll-m-20 text-2xl font-semibold tracking-tight",
            h4: "scroll-m-20 text-xl font-semibold tracking-tight",
            h5: "scroll-m-20 text-lg font-semibold tracking-tight",
            h6: "scroll-m-20 text-base font-semibold tracking-tight",
            /** Body ****************************************************************/
            p: "leading-7 [&:not(:first-child)]:mt-6",
            span: "leading-normal",
            small: "text-sm font-medium leading-none",
            lead: "text-xl text-muted-foreground",
            muted: "text-sm text-muted-foreground",
            /** Inline **************************************************************/
            code: "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm",
            /** Block ***************************************************************/
            blockquote: "mt-6 border-l-2 pl-6 italic",
        },

        /** Optional mix‑in variants **********************************************/
        align: {
            left: "text-left",
            center: "text-center",
            right: "text-right",
            justify: "text-justify",
        },
        weight: {
            normal: "font-normal",
            medium: "font-medium",
            semibold: "font-semibold",
            bold: "font-bold",
        },
        tone: {
            default: "",
            muted: "text-muted-foreground",
            destructive: "text-destructive",
            primary: "text-primary",
        },
    },
    /**
     * No default `variant`; user must choose (compile‑time safety).
     * Other variants default to Tailwind’s defaults (no classes).
     */
    defaultVariants: {
        align: undefined,
        weight: undefined,
        tone: "default",
    },
})

/** ------------------------------------------------------------------ */
/** React component wrapper                                            */
/** ------------------------------------------------------------------ */
export interface TypographyProps
    extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
    /**
     * Render as some other element instead of the semantic default.
     * The defaultTag map (below) picks sensible HTML tags for each variant,
     * but you are free to override them for a11y edge‑cases.
     */
    as?: React.ElementType
    /**
     * Adopt parent’s semantics while keeping these styles
     * (identical to shadcn/ui components).
     */
    asChild?: boolean
}

/** Map variant → default semantic tag */
const defaultTag: Record<
    NonNullable<TypographyProps["variant"]>,
    keyof React.JSX.IntrinsicElements
> = {
    h1: "h1",
    h2: "h2",
    h3: "h3",
    h4: "h4",
    h5: "h5",
    h6: "h6",
    p: "p",
    span: "span",
    small: "small",
    lead: "p",
    muted: "p",
    code: "code",
    blockquote: "blockquote",
}

export const Typography = React.forwardRef<HTMLElement, TypographyProps>(
    (
        {
            variant,
            align,
            weight,
            tone,
            as: Tag,
            asChild = false,
            className,
            ...props
        },
        ref,
    ) => {
        const Comp = asChild ? Slot : Tag ?? defaultTag[variant!]

        return (
            <Comp
                ref={ref as React.Ref<HTMLElement>}
                className={cn(
                    typographyVariants({ variant, align, weight, tone }),
                    className,
                )}
                {...props}
            />
        )
    },
)
Typography.displayName = "Typography"
