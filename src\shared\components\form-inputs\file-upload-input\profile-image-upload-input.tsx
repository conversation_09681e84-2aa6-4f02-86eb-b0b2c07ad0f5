import React from 'react'
import { Control, ControllerRenderProps, FieldPath, FieldValues, useFormContext } from 'react-hook-form'
import { FormControl, FormField, FormItem, FormMessage, useFormField } from '@/ui/form'
import { UserRound } from 'lucide-react'
import { cn } from '@/shared/lib/utils'
import { className as baseInputClassName } from '@/ui/input'
import {
    FileUpload,
    FileUploadDropzone,
    FileUploadItem,
    FileUploadItemPreview,
    FileUploadTrigger
} from '@/ui/file-upload'
import { INPUT_IMAGE_SIZE } from '@/shared/constants/input-image-size'
import Image from 'next/image'

interface ProfileImageUploadInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
    name: TName
    control: Control<TFieldValues>
    inputClassName?: string
}

function ProfileImageUploadInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    name,
    control,
    inputClassName,
}: ProfileImageUploadInputProps<TFieldValues, TName>) {
    const { setError } = useFormContext()
    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    <FormControl>
                        <FileUpload
                            value={field.value}
                            onValueChange={field.onChange}
                            accept="image/*"
                            maxFiles={1}
                            disabled={field.disabled}
                            onBlur={field.onBlur}
                            maxSize={INPUT_IMAGE_SIZE.PROFILE_PHOTO}
                            onFileReject={(file, message) => {
                                const maxSize = INPUT_IMAGE_SIZE.PROFILE_PHOTO
                                if (file.size > maxSize) {
                                    setError(name, {
                                        message: "Maximum file size is 1MB"
                                    })
                                    return
                                }
                                if (message === "Maximum 1 files allowed") {
                                    field.onChange([file])
                                    return
                                }
                                setError(name, {
                                    message: message
                                })
                            }}
                        >
                            <WrappedProfileImageUploadInput
                                field={field}
                                inputClassName={inputClassName}
                            />
                            <FormMessage className='text-center mt-1' />
                        </FileUpload>
                    </FormControl>
                </FormItem>
            )}
        />
    )
}

interface WrappedProfileImageUploadInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
    field: ControllerRenderProps<TFieldValues, TName>
    inputClassName?: string
}

function WrappedProfileImageUploadInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    field,
    inputClassName,
}: WrappedProfileImageUploadInputProps<TFieldValues, TName>) {
    const { error } = useFormField()

    return (
        <>
            {(!field.value || field.value.length === 0) && (
                <FileUploadDropzone
                    aria-invalid={!!error}
                    className={cn(
                        baseInputClassName,
                        "text-center text-muted-foreground cursor-pointer size-44 p-3 ",
                        inputClassName
                    )}
                    ref={field.ref}
                >
                    <UserRound className='size-14 text-muted-foreground/40' />
                </FileUploadDropzone>
            )}
            {field?.value?.map((file: File | string, index: number) => (
                <FileUploadDropzone
                    // className="flex gap-2 cursor-pointer p-3"
                    className={cn(
                        baseInputClassName,
                        "text-center text-muted-foreground cursor-pointer size-44 p-3",
                        inputClassName
                    )}
                    ref={field.ref}
                    key={`${index}-${field.value.name}-file-upload-dropzone`}
                    asChild
                    aria-invalid={!!error}
                >
                    {typeof file === "string" ? (
                        <div className=''>
                            <Image
                                src={file}
                                alt='profile image'
                                width={128}
                                height={128}
                                className='w-full h-full object-cover'
                            />
                        </div>
                    ) : (

                        <FileUploadItem
                            value={file}
                            className='flex items-center justify-center'
                        >
                            <FileUploadItemPreview
                                className={cn(
                                    'w-full h-full bg-transparent border-none',
                                    inputClassName
                                )}
                            />
                        </FileUploadItem>
                    )}
                </FileUploadDropzone>
            ))}
        </>
    )
}

export default ProfileImageUploadInput