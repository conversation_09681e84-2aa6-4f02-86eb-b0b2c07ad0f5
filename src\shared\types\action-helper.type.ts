// import { Schema } from "@/types/schema.type"

import { ZodType } from "zod"

/**
 * Type definition for the body of POST/PUT/PATCH requests
 * Represents a generic object with string keys and unknown values
 * @property {string} key - The property name in the request body
 * @property {unknown} value - The value associated with the property
 */
export type PostBody = Record<string, unknown>

/**
 * Type definition for the query parameters of GET requests
 * Represents a generic object with string keys and string/number/boolean/null/undefined values
 * @property {string} key - The property name in the query parameters
 * @property {string | number | boolean | null | undefined} value - The value associated with the property
 */
export type QueryParams = Record<string, string | number | boolean | null | undefined>

/**
 * Enum defining the available HTTP request methods
 * @property {string} get - HTTP GET method for retrieving data
 * @property {string} post - HTTP POST method for creating new resources
 * @property {string} put - HTTP PUT method for updating entire resources
 * @property {string} delete - HTTP DELETE method for removing resources
 * @property {string} patch - HTTP PATCH method for partial resource updates
 */
export enum REQUEST_TYPE {
    get = "GET",
    post = "POST",
    put = "PUT",
    delete = "DELETE",
    patch = "PATCH",
}

/**
 * Base type for all API request configurations
 * Contains common properties shared across all request types
 */
export type ExecuteApiRequestBaseProps = {
    /** The complete URL endpoint for the API request */
    url: string,
    /** The HTTP method to be used for the request */
    method: REQUEST_TYPE,
    /** Optional schema for validating the response data */
    schema?: ZodType,
    /** Additional fetch API options excluding url and method */
    options?: Omit<RequestInit, "url" | "method">,
    /** If true, the request will be made with authentication */
    withAuth?: boolean,
}

/**
 * Type for mutation request configurations
 * Contains common properties shared across all mutation request types
 */
export interface ExecuteMutationApiRequestProps extends ExecuteApiRequestBaseProps {
    /** The data to be sent in the request body */
    body: PostBody,
    /** If true, the body will be converted to FormData */
    isFormData?: boolean,
}

/**
 * Type for GET request configurations
 * Extends base props and adds query parameters
 */
export interface ExecuteGetApiRequestProps extends ExecuteApiRequestBaseProps {
    /** Must be GET method for this request type */
    method: REQUEST_TYPE.get,
    /** Optional query parameters to be appended to the URL */
    params?: Record<string, string | number | boolean | undefined | null>,
}

/**
 * Type for POST request configurations
 * Extends base props and adds request body
 */
export interface ExecutePostApiRequestProps extends ExecuteMutationApiRequestProps {
    /** Must be POST method for this request type */
    method: REQUEST_TYPE.post,
}

/**
 * Type for PUT request configurations
 * Extends base props and adds request body
 */
export interface ExecutePutApiRequestProps extends ExecuteMutationApiRequestProps {
    /** Must be PUT method for this request type */
    method: REQUEST_TYPE.put,
}

/**
 * Type for DELETE request configurations
 * Extends base props and adds optional parameters
 */
export interface ExecuteDeleteApiRequestProps extends ExecuteApiRequestBaseProps {
    /** Must be DELETE method for this request type */
    method: REQUEST_TYPE.delete,
    /** Optional parameters that might be needed for the delete operation */
    params?: Record<string, unknown>,
}

/**
 * Type for PATCH request configurations
 * Extends base props and adds request body
 */
export interface ExecutePatchApiRequestProps extends ExecuteMutationApiRequestProps {
    /** Must be PATCH method for this request type */
    method: REQUEST_TYPE.patch,
}

/**
 * Union type combining all possible API request configurations
 * Used for type checking when making API requests
 * @property {ExecuteGetApiRequestProps} - Configuration for GET requests
 * @property {ExecutePostApiRequestProps} - Configuration for POST requests
 * @property {ExecutePutApiRequestProps} - Configuration for PUT requests
 * @property {ExecuteDeleteApiRequestProps} - Configuration for DELETE requests
 * @property {ExecutePatchApiRequestProps} - Configuration for PATCH requests
 */
export type ExecuteApiRequestProps =
    ExecuteGetApiRequestProps |
    ExecutePostApiRequestProps |
    ExecutePutApiRequestProps |
    ExecuteDeleteApiRequestProps |
    ExecutePatchApiRequestProps


    