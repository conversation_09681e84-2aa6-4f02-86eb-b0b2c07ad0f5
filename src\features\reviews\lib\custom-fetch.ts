import { fetchClient } from "@/shared/lib/fetch-client";
import { ApiResponse } from "@/shared/types/utility.type";

interface CustomFetchProps {
  url: string;
  method?: string;
  body?: any;
  userToken?: string;
}

/**
 * Custom fetch function for reviews API that handles specific authentication requirements
 */
export async function fetchReviewsAPI<T>({ 
  url, 
  method = 'GET', 
  body,
  userToken 
}: CustomFetchProps): Promise<ApiResponse<T>> {
  try {
    // Custom headers for the reviews API
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add Authorization header if userToken is provided
    if (userToken) {
      headers['Authorization'] = `Bearer ${userToken}`;
    }

    // Add the specific cookie format mentioned in the API docs
    // Note: In production, these tokens should come from user session
    headers['Cookie'] = 'adminAuthToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.C08qBlIsKMFeOSRvLYklT8HzpWxsdpBzyJsS_oIwK8Y; authToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.rEBRQlIfQfBnqvh2I8xubKr8ehvqQIG3Kaztqt0VcVM';

    console.log('Making request to:', url);
    console.log('Headers:', headers);

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Response error:', errorText);
      return {
        success: false,
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
      };
    }

    const data = await response.json();
    console.log('Response data:', data);

    // Handle the specific API response format
    if (data.success) {
      return {
        success: true,
        data: data.data,
        message: data.message,
        status: data.status || response.status,
      };
    } else {
      return {
        success: false,
        message: data.message || 'API request failed',
        status: data.status || response.status,
      };
    }
  } catch (error) {
    console.error('Fetch error:', error);
    return {
      success: false,
      message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      status: 500,
    };
  }
}
