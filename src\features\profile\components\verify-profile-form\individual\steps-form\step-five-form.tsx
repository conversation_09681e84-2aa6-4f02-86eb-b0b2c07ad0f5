"use client"

import useIndividualRegistrationFormStore from '@/shared/context/individual-registration-form-store'
import React from 'react'
import { Step5FormType, step5Schema } from '@/features/profile/schemas/profile-registration/individual.schema'
import { FileUploadInput } from '@/shared/components/form-inputs/file-upload-input/file-upload-input'
import { INPUT_IMAGE_SIZE } from '@/shared/constants/input-image-size'
import DateInput from '@/shared/components/form-inputs/date-input'
import NavigationButton from '../navigation-button'
import { useFormSteps } from '@/shared/context/form-steps-context'
import { SELECT_OPTION_IDS } from '@/shared/constants/select-option-ids'
import { FormProvider, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

function StepFiveForm() {

    const { form: mainForm, updateStep } = useIndividualRegistrationFormStore()
    const { goToNextStep} = useFormSteps()
    const nationality = mainForm.step1.nationality
    const isUaeNational = nationality === SELECT_OPTION_IDS.UAE_NATIONALITY.toString()

    const form = useForm<Step5FormType>({
        resolver: zodResolver(step5Schema),
        defaultValues: mainForm.step5,
    })

    const { control } = form

    const onSubmit = (data: Step5FormType) => {
        if (!isUaeNational && !data.visa) {
            form.setError('visa', {
                message: "Required",
            })
            return
        }
        updateStep('step5', data)
        goToNextStep()
    }

    return (
       <FormProvider {...form}>
            <form 
                className='space-y-6'
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <FileUploadInput
                    name="passport"
                    control={control}
                    label="Passport"
                    maxSize={INPUT_IMAGE_SIZE.MB1}
                    maxFiles={1}
                />
                {!isUaeNational && <FileUploadInput
                    name="visa"
                    control={control}
                    label="Visa"
                    maxSize={INPUT_IMAGE_SIZE.MB1}
                    maxFiles={1}
                />}
                <FileUploadInput
                    name="emiratesId"
                    control={control}
                    label="Emirates ID"
                    maxSize={INPUT_IMAGE_SIZE.MB1}
                    maxFiles={2}
                    multiple
                />
                <DateInput
                    name="emiratesIdExpire"
                    control={control}
                    label="Emirates ID Expire"
                    disabledDate={(date) => date < new Date()}
                    fromYear={new Date().getFullYear()}
                    toYear={2030}
                />
                <NavigationButton />
            </form>
       </FormProvider>
    )
}

export default StepFiveForm 