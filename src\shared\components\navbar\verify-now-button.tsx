"use client"

import React from 'react'
import { But<PERSON> } from '../ui/button'
import { ShieldAlert } from 'lucide-react'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { useRouter } from 'next/navigation'

function VerifyNowButton() {
    const router = useRouter()
    return (
        <Button 
            className='h-9'
            onClick={() => {
                router.push(PAGE_ROUTES.completeProfile)
            }}
        >
            <ShieldAlert />
            Verify Now
        </Button>
    )
}

export default VerifyNowButton