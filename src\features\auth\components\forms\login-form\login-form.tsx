"use client"

import React from 'react'
import { TextInput } from '@/form-inputs/text-input'
import { LoginFormSchema, loginFormSchema } from '@/auth/schemas/forms/login-form.schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { Button } from '@/ui/button'
import Link from 'next/link'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { handleApiResponse } from '@/helpers/client.helpers'
import { loginAgent } from '@/auth/actions/auth.action'
import { CLIENT_SUCCESS_MESSAGES } from '@/constants/messages/client'

function LoginForm() {
    // Initializing the login form
    const form = useForm<LoginFormSchema>({
        resolver: zodResolver(loginFormSchema),
        defaultValues: {
            email: "",
            password: "",
        },
    })

    const {
        control,
        formState: { isSubmitting }
    } = form

    const onSubmit = async (data: LoginFormSchema) => {
        const res = await loginAgent(data)
        handleApiResponse({
            response: res,
            successMessage: CLIENT_SUCCESS_MESSAGES.loginSuccess,
            onSuccess: () => {
                window.location.href = PAGE_ROUTES.dashboard
            },
        })
    }


    return (
        <FormProvider {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='flex flex-col gap-5'
                noValidate
            >
                <TextInput
                    label="Email"
                    name="email"
                    control={control}
                    type='email'
                />
                <div className="flex flex-col gap-1">
                    <TextInput
                        label="Password"
                        name="password"
                        control={control}
                        type='password'
                    />
                    <Link
                        href="/forgot-password"
                        className='text-xs text-muted-foreground text-end self-end hover:underline'
                    >
                        Forgot Password?
                    </Link>
                </div>

                <Button
                    type="submit"
                    isLoading={isSubmitting}
                    className='mt-2'
                    size={"lg"}
                >
                    {isSubmitting ? "Logging In" : "Log In"}
                </Button>


            </form>
        </FormProvider>
    )
}

export default LoginForm