"use client"

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    LogOut,
    Settings,
    User,
} from "lucide-react";
import { shortenName } from "@/lib/utils";
import { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/ui/dialog";
import { Button } from "@/ui/button";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { logoutUser } from "@/features/auth/actions/auth.action";
import { PAGE_ROUTES } from "@/constants/page-routes";
import { handleApiResponse } from "@/lib/helpers/client.helpers";
import { useSession } from "@/shared/context/session-context";
import { Skeleton } from "../ui/skeleton";


export default function AvatarDropdownMenu() {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(false)
    const [isOpen, setIsOpen] = useState(false)
    const { session, isLoading: isSessionLoading } = useSession()

    const logoutClickHandler = async () => {
        setIsLoading(true)
        const response = await logoutUser()
        handleApiResponse({
            response,
            onSuccess: () => {
                router.push(PAGE_ROUTES.login)
            },
            finallyHandler: () => {
                setIsLoading(false)
            }
        })
    }

    const setIsLogoutPopupOpenHandler = (state: boolean) => {
        if (isLoading) {
            setIsOpen(true)
            return
        }
        setIsOpen(state)
    }

    // If session loading show a skeleton
    if (isSessionLoading) {
        return (
            <Skeleton className="size-10 rounded-full aspect-square bg-muted" />
        )
    }

    if (!session) {
        return (
            <Avatar className="size-10">
                <AvatarFallback className="bg-background hover:bg-background-dark">
                    <User className="size-4" />
                </AvatarFallback>
            </Avatar>
        )
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsLogoutPopupOpenHandler}>
            <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-3 ring-ring/50 outline-none cursor-pointer rounded-full focus-visible:ring-4">
                    <Avatar className="size-10">
                        <AvatarImage src={session?.profileImage || undefined} className="object-cover" />
                        <AvatarFallback className="bg-background hover:bg-background-dark">
                            {shortenName(session.name)}
                        </AvatarFallback>
                    </Avatar>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="mt-2 w-72 mx-3">
                    <DropdownMenuItem className="py-3">
                        {/* <Avatar className="size-10">
                            <AvatarImage src={session?.profileImage || undefined} className="object-cover" />
                            <AvatarFallback className="bg-background hover:bg-background-dark">
                                {shortenName(session.name)}
                            </AvatarFallback>
                        </Avatar> */}
                        <div className="ml-1 flex flex-col">
                            <p className="text-sm font-medium">{session.name}</p>
                            <p className="text-xs text-muted-foreground">
                                {session.email}
                            </p>
                        </div>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                        <User className="mr-1" /> View Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                        <Settings className="mr-1" /> Settings
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {
                        setIsOpen(true)
                    }}>
                        <LogOut className="mr-1" /> Sign out
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
            <DialogContent
                onInteractOutside={(e) => {
                    if (isLoading) {
                        e.preventDefault()
                    }
                }}
                className='rounded-md'
            >
                <DialogHeader>
                    <DialogTitle className="pb-0">
                        Logout?
                    </DialogTitle>
                    <DialogDescription>
                        Are you sure you want to logout?
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button
                            variant={"outline"}
                            className="h-9"
                        >
                            Cancel
                        </Button>
                    </DialogClose>
                    <Button
                        onClick={logoutClickHandler}
                        isLoading={isLoading}
                        className="h-9"
                    >
                        Log out
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
