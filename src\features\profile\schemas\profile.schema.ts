import { fileSchema, fileSchemaArray, phoneSchema, selectSchema, textSchema } from "@/lib/schemas/common.schema"
import { z } from "zod"
import { GENDER_OPTIONS } from "@/constants/select-options"
import { INPUT_ERROR_MESSAGES } from "@/constants/messages/client"
import { SELECT_OPTION_IDS } from "@/constants/select-option-ids"
import { makeOptionalField } from "@/shared/lib/helpers/schema.helpers"
import { licenseRecordSchema } from "./license.schema"
import { INPUT_IMAGE_SIZE } from "@/shared/constants/input-image-size"


export const form1Schema = z.object({
    firstName: textSchema,
    lastName: textSchema,
    middleName: textSchema.optional(),
    phoneNumber: phoneSchema,
    nationality: textSchema,
    gender: selectSchema(GENDER_OPTIONS),
    location: textSchema,
    profilePhoto: fileSchemaArray(INPUT_IMAGE_SIZE.PROFILE_PHOTO),
})

export const form2Schema = z
    .object({
        primaryIndustries: z.array(z.string())
            .min(1, {
                error: INPUT_ERROR_MESSAGES.required,
            })
            .max(3, {
                error: INPUT_ERROR_MESSAGES.maxThreeItems,
            }),
        otherPrimaryIndustry: z.array(textSchema),

    })
    .refine((data) => {
        const otherOptionsId = SELECT_OPTION_IDS.OTHER_PRIMARY_INDUSTRY.toString()
        if (data.primaryIndustries.includes(otherOptionsId)) {
            return !!data.otherPrimaryIndustry
        }
        return true
    }, {
        message: INPUT_ERROR_MESSAGES.required,
        path: ["otherPrimaryIndustry"],
    })


export const form3Schema = z.record(z.string(), textSchema)

export const form4Schema = licenseRecordSchema

export const form5Schema = z.object({
})

export const form6Schema = z.object({
})


export const profileSchema = z.object({
    form1: form1Schema,
    form2: form2Schema,
    form3: form3Schema,
    form4: form4Schema,
    form5: form5Schema,
    form6: form6Schema,
})


export type Form1Schema = z.infer<typeof form1Schema>
export type Form2Schema = z.infer<typeof form2Schema>
export type Form3Schema = z.infer<typeof form3Schema>
export type Form4Schema = z.infer<typeof form4Schema>
export type Form5Schema = z.infer<typeof form5Schema>
export type Form6Schema = z.infer<typeof form6Schema>


export type ProfileSchema = z.infer<typeof profileSchema>
