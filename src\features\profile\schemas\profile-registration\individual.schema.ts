import { dateSchema, fileSchemaArray, multiSelectSchema, phoneSchema, selectSchema, textSchema } from "@/lib/schemas/common.schema"
import { z } from "zod"
import { CONFIRMATION_OPTIONS, GENDER_OPTIONS, YES_NO_OPTIONS } from "@/constants/select-options"
import { INPUT_ERROR_MESSAGES } from "@/constants/messages/client"
import { SELECT_OPTION_IDS } from "@/constants/select-option-ids"
import { makeOptionalField } from "@/shared/lib/helpers/schema.helpers"
import { INPUT_IMAGE_SIZE } from "@/shared/constants/input-image-size"
import { licenseRecordSchema, licenseSchema } from "../license.schema"


export const step1Schema = z.object({
    firstName: textSchema,
    lastName: textSchema,
    middleName: makeOptionalField(textSchema),
    phoneNumber: phoneSchema,
    nationality: textSchema,
    gender: selectSchema(GENDER_OPTIONS),
    location: textSchema,
    profilePhoto: fileSchemaArray(INPUT_IMAGE_SIZE.PROFILE_PHOTO),
})

export const step2Schema = z
    .object({
        primaryIndustries: z.array(z.string())
            .min(1, {
                error: INPUT_ERROR_MESSAGES.required,
            })
            .max(3, {
                error: INPUT_ERROR_MESSAGES.maxThreeItems,
            }),
        otherPrimaryIndustry: z.array(textSchema),

    })
    .refine((data) => {
        const otherOptionsId = SELECT_OPTION_IDS.OTHER_PRIMARY_INDUSTRY.toString()
        if (data.primaryIndustries.includes(otherOptionsId)) {
            return !!data.otherPrimaryIndustry
        }

        return true
    }, {
        message: INPUT_ERROR_MESSAGES.required,
        path: ["otherPrimaryIndustry"],
    })

export const step3Schema = z.object({
    defaultIndustryRoles: z.record(
        z.string(),
        textSchema,
    )
        .describe("This is the default role for each default industry"),

    defaultIndustryOtherRoles: z.record(
        z.string(),
        textSchema
    )
        .optional()
        .describe("This is the other role for each default industry"),

    otherIndustryRoles: z.record(
        z.string(),
        textSchema
    )
        .optional()
        .describe("This is the other role for other industry"),
})


export const step4Schema = z.object({
    defaultIndustryRoles: z
        .record(
            z.string(),
            licenseSchema,
        )
        .describe("License for the default role for each default industry"),

    defaultIndustryOtherRoles: z
        .record(
            z.string(),
            licenseSchema
        )
        .optional()
        .describe("License for the other role for each default industry"),

    otherIndustryRoles: z
        .record(
            z.string(),
            licenseSchema
        )
        .optional()
        .describe("License for the other role for other industry"),

})


export const step5Schema = z.object({
    emiratesId: fileSchemaArray(INPUT_IMAGE_SIZE.MB1).min(1, {
        error: "Required",
    }),
    emiratesIdExpire: dateSchema,
    passport: fileSchemaArray(INPUT_IMAGE_SIZE.MB1),
    visa: fileSchemaArray(INPUT_IMAGE_SIZE.MB1).optional(),
})


export const step6Schema = z.object({
    confirmation: multiSelectSchema(CONFIRMATION_OPTIONS)
        .refine(data => {
            return data.includes("termsAndConditions") && data.includes("confirmDetails")
        }, {
            message: "Required",
            path: ["confirmation"],
        })
})


export const individualProfileSchema = z.object({
    step1: step1Schema,
    step2: step2Schema,
    step3: step3Schema,
    step4: step4Schema,
    step5: step5Schema,
    step6: step6Schema,
})


export type Step1FormType = z.infer<typeof step1Schema>
export type Step2FormType = z.infer<typeof step2Schema>
export type Step3FormType = z.infer<typeof step3Schema>
export type Step4FormType = z.infer<typeof step4Schema>
export type Step5FormType = z.infer<typeof step5Schema>
export type Step6FormType = z.infer<typeof step6Schema>


export type IndividualProfileSchema = z.infer<typeof individualProfileSchema>
