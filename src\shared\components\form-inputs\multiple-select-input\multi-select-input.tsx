import { <PERSON><PERSON><PERSON> } from '@/components/ui/form'
import { FieldPath, FieldValues } from 'react-hook-form'
import { Control } from 'react-hook-form'
import React from 'react'
import MultiSelectInputWithGroup, { MultiSelectInputWithGroupProps } from './multi-select-input-with-group'
import { MultiSelectProps } from '@/components/ui/multi-select'

/**
 * Interface for the MultiSelectInput component
 * Extends the MultiSelectInputWithGroupProps while omitting 'multiSelectProps'
 * to provide our own typed version that integrates with react-hook-form.
 */
interface MultiSelectInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
    > extends Omit<MultiSelectInputWithGroupProps, 'multiSelectProps'> {
    /** React Hook Form control object for form state management */
    control: Control<TFieldValues>,
    
    /** The name/path of the field in the form schema */
    name: TName,
    
    /** Array of selectable options for the multi-select dropdown */
    options: MultiSelectProps['options']
    
    /** Placeholder text displayed when no options are selected */
    placeholder?: string
    
    /** Maximum number of options that can be selected simultaneously */
    maxOptionsSelected?: number

    /** If true, the select all option will be hidden. */
    hideSelectAllOption?: boolean

}

/**
 * MultiSelectInput Component
 * 
 * A production-ready multi-select input component that integrates seamlessly with React Hook Form.
 * This component wraps the base MultiSelectInputWithGroup component and provides form field
 * binding, validation, and state management through react-hook-form.
 * 
 * Features:
 * - Full TypeScript support with generic form typing
 * - React Hook Form integration for validation and state management
 * - Customizable maximum selection limit
 * - Modal popover for better UX on mobile devices
 * - Accessible form field structure
 * 
 * @template TFieldValues - The complete form data type
 * @template TName - The specific field name type for type safety
 * 
 * @param props - The component props
 * @returns A form-integrated multi-select input component
 * 
 * @example
 * ```tsx
 * <MultiSelectInput
 *   control={form.control}
 *   name="skills"
 *   options={skillOptions}
 *   placeholder="Select your skills"
 *   maxOptionsSelected={5}
 *   label="Technical Skills"
 * />
 * ```
 */
function MultiSelectInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    control,
    name,
    options,
    placeholder,
    maxOptionsSelected,
    hideSelectAllOption,
    ...formGroupProps // Spread remaining props for form group
}: MultiSelectInputProps<TFieldValues, TName>) {
    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <MultiSelectInputWithGroup
                    {...formGroupProps}
                    multiSelectProps={{
                        // Enable modal popover for better mobile experience
                        modalPopover: true,
                        
                        // Connect react-hook-form field handlers
                        onValueChange: field.onChange,
                        onBlur: field.onBlur,
                        defaultValue: field.value,
                        ref: field.ref, 
                        
                        // Options for the multi-select dropdown
                        options,

                        // Placeholder text
                        placeholder,

                        // Maximum number of options that can be selected
                        maxOptionsSelected,

                        // Hide the select option
                        hideSelectOption: hideSelectAllOption,

                    }}
                />
            )}
        />
    )
}

export default MultiSelectInput