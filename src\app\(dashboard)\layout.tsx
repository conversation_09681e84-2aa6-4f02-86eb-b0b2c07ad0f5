import React from 'react'
import { Navbar } from '@/components/navbar'
import { DashboardSidebar } from '@/components/dashboard-sidebar'
import { SidebarProvider } from '@/components/ui/sidebar'

function DashboardLayout({ children }: { children: React.ReactNode }) {
    return (
        <SidebarProvider>
            <DashboardSidebar />
            <div className={`relative w-full grid h-svh grid-rows-[var(--height-nav)_1fr] overflow-hidden `}>
                <Navbar />
                <div className='px-dashboard-layout py-5 overflow-auto bg-background w-full grid'>
                    {children}
                </div>
            </div>
        </SidebarProvider>
    )
}

export default DashboardLayout