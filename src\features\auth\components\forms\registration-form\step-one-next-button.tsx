"use client"

import { But<PERSON> } from '@/components/ui/button'
import { RegistrationFormSchema } from '@/features/auth/schemas/forms/registration-form.schema'
import React from 'react'
import { useFormContext } from 'react-hook-form'
import { useRegistrationFormSteps } from '@/auth/context/registration-form-steps-context'

function StepOneNextButton() {
    const { watch } = useFormContext<RegistrationFormSchema>()
    const { goToNextStep } = useRegistrationFormSteps()
    const accountType = watch("accountType")

    const handleNext = () => {
        if(accountType) {
            goToNextStep()
        }
    }

    return (
        <Button
            type='button'
            onClick={handleNext}
            disabled={!accountType}
        >
            Next
        </Button>
    )
}

export default StepOneNextButton