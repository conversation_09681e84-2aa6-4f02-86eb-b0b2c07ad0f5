import { AUTH_COOKIE_NAME } from "@/constants/cookie-names"
import { Parsed<PERSON><PERSON><PERSON> } from "@/shared/types/cookies-helpers.type";
import { cookies } from "next/headers"


/**
 * Function to remove the auth cookie from the cookie store
 * @returns void
 */
export async function removeAuthCookie(): Promise<void> {
    const cookieStore = await cookies()
    cookieStore.set(AUTH_COOKIE_NAME, '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 0,
        path: '/',
    })
}


/**
 * Extract a single cookie (and its attributes) from a Fetch `Response`.
 *
 * @param key          The cookie name you are looking for.
 * @param res          The Fetch Response that potentially carries one or more `Set‑Cookie` headers.
 * @returns            A fully‑parsed cookie object, or `null` when the cookie is absent.
 */
export function getCookieFromResponse(
    key: string,
    res: Response
): ParsedCookie | null {
    /* -----------------------------------------------------------
     * 1. Collect every Set‑Cookie header line.
     *    ‑ In Node ≥20 the WHATWG Headers object supplies .getSetCookie().
     *    ‑ Fallback to .get('set-cookie') when the polyfill aggregates
     *      multiple headers into a single comma‑separated string.
     * ---------------------------------------------------------- */
    const headerLines: string[] | null =
        // @ts‑ignore – Node/Fast‑Fetch feature
        typeof res.headers.getSetCookie === "function"
            ? res.headers.getSetCookie()
            : res.headers.get("set-cookie")?.split(/,(?=[^;]+?=)/) ?? null;

    if (!headerLines?.length) return null;

    /* -----------------------------------------------------------
     * 2. Walk through every Set‑Cookie header, parse it, stop once
     *    we find the requested key.
     * ---------------------------------------------------------- */
    for (const line of headerLines) {
        // <name>=<value>; Attr=...; Attr=...
        const segments = line.split(";").map(s => s.trim());

        const [name, value] = segments[0].split("=");
        if (name !== key) continue;

        const parsed: ParsedCookie = { name, value };

        for (const attr of segments.slice(1)) {
            const [attrKey, attrVal] = attr.split("=");
            switch (attrKey.toLowerCase()) {
                case "expires":
                    parsed.expires = new Date(attrVal); // RFC 1123
                    break;
                case "max-age":
                    parsed.maxAge = Number(attrVal);
                    break;
                case "path":
                    parsed.path = attrVal;
                    break;
                case "domain":
                    parsed.domain = attrVal;
                    break;
                case "samesite":
                    // Capitalise for consistency.
                    parsed.sameSite = (attrVal ?? "Lax")
                        .replace(/^\w/, c => c.toUpperCase()) as ParsedCookie["sameSite"];
                    break;
                case "secure":
                    parsed.secure = true;
                    break;
                case "httponly":
                    parsed.httpOnly = true;
                    break;
                // Ignore unknown attributes silently.
            }
        }
        return parsed;
    }

    // Not found.
    return null;
}


export async function setCookie(cookie: ParsedCookie): Promise<void> {
    const cookieStore = await cookies()
    
    // Convert sameSite to lowercase to match Next.js cookies API expectations
    const sameSite = cookie.sameSite?.toLowerCase() as "strict" | "lax" | "none" | undefined
    
    cookieStore.set(cookie.name, cookie.value, {
        httpOnly: cookie.httpOnly ?? true,
        secure: cookie.secure ?? (process.env.NODE_ENV === "production"),
        path: cookie.path ?? "/",
        sameSite: sameSite ?? "strict",
    })
}

export async function getAuthCookie(): Promise<ParsedCookie | null> {
    const cookieStore = await cookies()
    return cookieStore.get(AUTH_COOKIE_NAME) ?? null
}