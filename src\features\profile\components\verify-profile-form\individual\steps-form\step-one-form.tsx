"use client"

import React from 'react'
import { TextInput } from '@/components/form-inputs/text-input'
import { Form<PERSON>rov<PERSON>, useForm, useFormContext } from 'react-hook-form'
import { ProfileSchema } from '@/features/profile/schemas/profile.schema'
import SelectInput from '@/components/form-inputs/select-input/select-input'
import PhoneInput from '@/components/form-inputs/phone-input/phone-input'
import { GENDER_OPTIONS } from '@/constants/select-options'
import { useCompleteProfileFormOptions } from '@/features/profile/context/complete-profile-form-options-context'
import ProfileImageUploadInput from '@/shared/components/form-inputs/file-upload-input/profile-image-upload-input'
import { IndividualProfileSchema, Step1FormType, step1Schema } from '@/features/profile/schemas/profile-registration/individual.schema'
import { zodResolver } from '@hookform/resolvers/zod'
import useIndividualRegistrationFormStore from '@/shared/context/individual-registration-form-store'
import { useFormSteps } from '@/shared/context/form-steps-context'
import { Button } from '@/shared/components/ui/button'
import NavigationButton from '../navigation-button'


function StepOneForm() {
    const { updateStep, form: mainForm } = useIndividualRegistrationFormStore()
    const { nationalities, locations } = useCompleteProfileFormOptions()
    const { goToNextStep } = useFormSteps()

    const form = useForm<Step1FormType>({
        resolver: zodResolver(step1Schema),
        defaultValues: mainForm.step1,
    })

    const {
        control,
        handleSubmit,
    } = form

    const onSubmit = (data: Step1FormType) => {
        updateStep('step1', data)
        goToNextStep()
    }

    return (
        <FormProvider {...form}>
            <form 
                className="flex flex-col gap-6 items-center"
                onSubmit={handleSubmit(onSubmit)}
                noValidate
            >
                <ProfileImageUploadInput 
                    name={`profilePhoto`}
                    control={control}
                    inputClassName='justify-center'
                />
                <TextInput
                    name={`firstName`}
                    label="First Name"
                    control={control}
                />
                <TextInput
                    name={`middleName`}
                    label="Middle Name"
                    control={control}
                />
                <TextInput
                    name={`lastName`}
                    label="Last Name"
                    control={control}
                />
                <SelectInput
                    name={`nationality`}
                    label="Nationality"
                    control={control}
                    options={nationalities.map((nationality) => ({
                        label: `${nationality.name} (${nationality.nationality})`,
                        value: nationality.id.toString(),
                    }))}
                />
                <SelectInput
                    name={`location`}
                    label="Location"
                    control={control}
                    options={locations.map((location) => ({
                        label: `${location.name} (${location.local})`,
                        value: location.id.toString(),
                    }))}
                />
                <SelectInput
                    name={`gender`}
                    label="Gender"
                    control={control}
                    options={GENDER_OPTIONS}
                />
                <PhoneInput
                    name={`phoneNumber`}
                    label="Phone Number"
                    control={control}
                />
                <NavigationButton />
            </form>
        </FormProvider>
    )
}

export default StepOneForm 