'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface FormStepsContextType {
    currentStep: number;
    totalSteps: number;
    goToNextStep: () => void;
    goToPreviousStep: () => void;
    goToStep: (step: number) => void;
    hasNextStep: boolean;
    hasPreviousStep: boolean;
    isFirstStep: boolean;
    isLastStep: boolean;
    progressPercentage: number;
}

const FormStepsContext = createContext<FormStepsContextType | undefined>(undefined);

interface FormStepsProviderProps {
    children: ReactNode;
    totalSteps?: number;
    initialStep?: number;
}

export function FormStepsProvider({
    children,
    totalSteps = 6,
    initialStep = 1,
}: FormStepsProviderProps) {
    const [currentStep, setCurrentStep] = useState(initialStep);

    const hasNextStep = currentStep < totalSteps;
    const hasPreviousStep = currentStep > 1;
    const isFirstStep = currentStep === 1;
    const isLastStep = currentStep === totalSteps;
    const progressPercentage = (currentStep / totalSteps) * 100;

    const goToNextStep = () => {
        if (hasNextStep) {
            setCurrentStep(currentStep + 1);
        }
    }

    const goToPreviousStep = () => {
        if (hasPreviousStep) {
            setCurrentStep(current => current - 1);
        }
    }

    const goToStep = (step: number) => {
        if (step >= 1 && step <= totalSteps) {
            setCurrentStep(step);
        }
    }

    const value: FormStepsContextType = {
        currentStep,
        totalSteps,
        goToNextStep,
        goToPreviousStep,
        goToStep,
        hasNextStep,
        hasPreviousStep,
        isFirstStep,
        isLastStep,
        progressPercentage,
    };

    return (
        <FormStepsContext.Provider value={value}>
            {children}
        </FormStepsContext.Provider>
    );
};

export const useFormSteps = (): FormStepsContextType => {
    const context = useContext(FormStepsContext);

    if (context === undefined) {
        throw new Error('useFormSteps must be used within a FormStepsProvider');
    }

    return context;
}; 