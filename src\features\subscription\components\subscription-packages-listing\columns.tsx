import {
    ColumnDef,
} from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PropertyListing } from "./data";
import { SortableHeader } from "./table-header";
import { Badge } from "@/shared/components/ui/badge";
import { returnVariantForStatus } from "./helpers";
import { 
    Dialog, 
    DialogClose, 
    DialogContent, 
    DialogDescription, 
    DialogFooter, 
    DialogHeader, 
    DialogTitle, 
    DialogTrigger 
} from "@/shared/components/ui/dialog";
import { Button } from "@/shared/components/ui/button";
import {
    MoreHorizontal,
    Eye,
    Share,
    Pencil,
    Trash2,
} from "lucide-react";
import { useIsMobile } from "@/shared/hooks/use-mobile";

export const columnsHeader: Record<keyof PropertyListing, string> = {
    id: "ID",
    property: "Property",
    type: "Type",
    location: "Location",
    listing: "Listing",
    price: "Price",
    postedBy: "Posted By",
    status: "Status",
}



export function useColumns() {
    const isMobile = useIsMobile()
    const columnsDef: ColumnDef<PropertyListing>[] = [
        // property column
        {
            accessorKey: "property",
            header: ({ column }) => <SortableHeader label={columnsHeader["property"]} column={column} />,
            cell: ({ row }) => (
                <div className="max-w-[340px] truncate whitespace-pre-wrap text-ellipsis">
                    {row.getValue("property")}
                </div>
            ),
        },
        // type
        {
            accessorKey: "type",
            header: columnsHeader["type"],
            cell: ({ row }) => {
                const { type, category } = row.getValue("type") as PropertyListing["type"];

                return (
                    <div className="flex gap-1 items-center md:flex-col md:items-stretch md:gap-0">
                        <span className="md:font-medium">{type}</span>
                        <span className="text-muted-foreground text-xs">({category})</span>
                    </div>
                )
            }
        },
        // location
        {
            accessorKey: "location",
            header: columnsHeader["location"],
        },
        // listing
        {
            accessorKey: "listing",
            header: columnsHeader["listing"],
            cell: ({ row }) => {
                const listing = row.getValue("listing") as string;
                const listingType = listing.split(" · ");
                return (
                    <div className="flex gap-1 items-center md:flex-col md:items-start">
                        <span className="md:font-medium">{listingType[0]}</span>
                        <span className="text-muted-foreground text-xs">({listingType[1]})</span>
                    </div>
                )
            },
        },
        // price with right align sortable
        {
            accessorKey: "price",
            header: ({ column }) => <SortableHeader label={columnsHeader["price"]} column={column} align="right" />,
            cell: ({ row }) => {
                const amount = parseFloat(row.getValue("price"));
                const formatted = new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "AED",
                    minimumFractionDigits: 0,
                }).format(amount);
                return <div className="md:font-medium">{formatted}</div>;
            },
        },
        // posted by
        {
            accessorKey: "postedBy",
            header: columnsHeader["postedBy"],
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue("postedBy")}</div>,
        },
        // status badge
        {
            accessorKey: "status",
            header: columnsHeader["status"],
            cell: ({ row }) => {
                const status: PropertyListing["status"] = row.getValue("status");
                return <Badge variant={returnVariantForStatus(status)}>{status}</Badge>;
            },
        },
        // actions
        {
            id: "actions",
            header: "Actions",
            enableHiding: false,
            cell: ({ row }) => {
                const listing = row.original;
                if (isMobile) {
                    return (
                        <div className="grid grid-cols-4 gap-2 justify-center md:hidden">
                            <Button
                                variant={"soft-muted"}
                                size="icon"
                                className="w-full"
                            >
                                <Eye />
                            </Button>
                            <Button
                                variant={"soft-muted"}
                                size="icon"
                                className="w-full"
                            >
                                <Share />
                            </Button>
                            <Button
                                variant={"soft-muted"}
                                size="icon"
                                className="w-full"
                            >
                                <Pencil />
                            </Button>
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button
                                        variant={"soft-muted"}
                                        size="icon"
                                        className="w-full text-destructive/60"
                                    >
                                        <Trash2 />
                                    </Button>
                                </DialogTrigger>
                                <DialogContent
                                    onInteractOutside={(e) => {
                                        // if (isLoading) {
                                        //     e.preventDefault()
                                        // }
                                    }}
                                    className='rounded-md'
                                >
                                    <DialogHeader>
                                        <DialogTitle className="pb-0">
                                            Delete Property
                                        </DialogTitle>
                                        <DialogDescription>
                                            Are you sure you want to delete this property? This action cannot be undone.
                                        </DialogDescription>
                                    </DialogHeader>
                                    <DialogFooter className="">
                                        <DialogClose asChild>
                                            <Button
                                                variant={"outline"}
                                                className="h-9"
                                            >
                                                Cancel
                                            </Button>
                                        </DialogClose>
                                        <Button
                                            // onClick={logoutClickHandler}
                                            // isLoading={isLoading}
                                            className="h-9"
                                            variant="destructive"
                                        >
                                            Delete
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>

                            </Dialog>
                        </div>
                    )
                }
                return (
                    <Dialog>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button
                                    variant="none"
                                    // size={"icon"}
                                    className="w-full p-0 mx-auto bg-muted md:bg-transparent md:size-7"
                                >
                                    <span className="sr-only">Open menu</span>
                                    <span className="block md:hidden">Actions</span>
                                    <MoreHorizontal className="hidden md:block" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-44">
                                <DropdownMenuItem>
                                    <Eye />
                                    View
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                    <Share />
                                    Share
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                    <Pencil />
                                    Edit
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DialogTrigger asChild>
                                    <DropdownMenuItem variant="destructive" className="text-destructive">
                                        <Trash2 />
                                        Delete
                                    </DropdownMenuItem>
                                </DialogTrigger>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <DialogContent
                            onInteractOutside={(e) => {
                                // if (isLoading) {
                                //     e.preventDefault()
                                // }
                            }}
                            className='rounded-md'
                        >
                            <DialogHeader>
                                <DialogTitle className="pb-0">
                                    Delete Property
                                </DialogTitle>
                                <DialogDescription>
                                    Are you sure you want to delete this property? This action cannot be undone.
                                </DialogDescription>
                            </DialogHeader>
                            <DialogFooter className="">
                                <DialogClose asChild>
                                    <Button
                                        variant={"outline"}
                                        className="h-9"
                                    >
                                        Cancel
                                    </Button>
                                </DialogClose>
                                <Button
                                    // onClick={logoutClickHandler}
                                    // isLoading={isLoading}
                                    className="h-9"
                                    variant="destructive"
                                >
                                    Delete
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                );
            },
        },
    ];
    return columnsDef
}