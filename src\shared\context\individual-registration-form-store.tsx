"use client"

import { IndividualProfileSchema, Step1FormType, Step2FormType, Step3FormType, Step4FormType, Step5FormType, Step6FormType } from '@/features/profile/schemas/profile-registration/individual.schema'
import { createContext, Dispatch, SetStateAction, useContext, useEffect, useState } from 'react'


type IndividualRegistrationFormStoreContextType = {
    form: IndividualProfileSchema
    setForm: Dispatch<SetStateAction<IndividualProfileSchema>>
    updateStep: (step: keyof IndividualProfileSchema, data: unknown) => void
}


export const IndividualRegistrationFormStoreContext = createContext<IndividualRegistrationFormStoreContextType | null>(null)


export const IndividualRegistrationFormStoreProvider = ({ children }: { children: React.ReactNode }) => {
    const [form, setForm] = useState<IndividualProfileSchema>({
        step1: {} as Step1FormType,
        step2: {} as Step2FormType,
        step3: {} as Step3FormType,
        step4: {} as Step4FormType,
        step5: {} as Step5FormType,
        step6: {} as Step6FormType,
    })

    const updateStep = (step: keyof IndividualProfileSchema, data: unknown) => {
        setForm(prev => {
            const newForm = { 
                ...prev,
                [step]: data, 
            }
            return newForm
        })
    }

    return (
        <IndividualRegistrationFormStoreContext.Provider 
            value={{ 
                form, 
                setForm,
                updateStep,
            }}
        >
            {children}
        </IndividualRegistrationFormStoreContext.Provider>
    )
}


const useIndividualRegistrationFormStore = () => {
    const context = useContext(IndividualRegistrationFormStoreContext)
    if (!context) {
        throw new Error('useIndividualRegistrationFormStore must be used within a IndividualRegistrationFormStoreProvider')
    }
    return context
}


export default useIndividualRegistrationFormStore