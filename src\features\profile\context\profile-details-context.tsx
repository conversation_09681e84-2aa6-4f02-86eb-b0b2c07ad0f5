"use client"

import { createContext, useContext } from "react";
import { ApiProfileDetails } from "../types/profile.type";

interface ProfileDetailsContextType {
    profileDetails: ApiProfileDetails
}

export const ProfileDetailsContext = createContext<ProfileDetailsContextType | null>(null)

export function ProfileDetailsContextProvider(props: { 
    children: React.ReactNode, 
    profileDetails: ApiProfileDetails 
}) {
    const { children, profileDetails } = props

    return (
        <ProfileDetailsContext.Provider value={{ profileDetails }}>
            {children}
        </ProfileDetailsContext.Provider>
    )
}

export function useProfileDetails() {
    const context = useContext(ProfileDetailsContext)
    if (!context) {
        throw new Error("useProfileDetails must be used within a ProfileDetailsContextProvider")
    }
    return context
}