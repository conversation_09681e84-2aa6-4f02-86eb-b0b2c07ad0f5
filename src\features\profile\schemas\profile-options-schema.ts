import { z } from "zod"

export const apiIndustryOptionSchema = z.object({
    id: z.number(),
    group: z.string().nullable(),
    name: z.string(),
    description: z.string(),
    typeId: z.number(),
    statusId: z.number(),
    createdBy: z.number(),
    createdOn: z.string(),
    modifiedOn: z.string().nullable(),
    parentId: z.number().nullable(),
})

export const apiIndustryOptionValueSchema = apiIndustryOptionSchema.partial().required({
    id: true,
    name: true,
})

export const apiIndustryRoleSchema = z.object({
    id: z.number(),
    name: z.string(),
    parentId: z.number().optional(),
    group: z.string().optional(),
})

