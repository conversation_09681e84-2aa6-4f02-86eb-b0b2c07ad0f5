"use client"

import React from 'react'
import StepOneForm from './steps-form/step-one-form'
import { useFormSteps } from '@/shared/context/form-steps-context'
import RenderStepsHeader from '../render-steps-header'
import { INDIVIDUAL_STEPS_HEADING } from '@/features/profile/constants/steps-headings'
import { Button } from '@/shared/components/ui/button'
import StepTwoForm from './steps-form/step-two-form'
import StepThreeForm from './steps-form/step-three-form'
import StepFourForm from './steps-form/step-four-form'
import StepFiveForm from './steps-form/step-five-form'
import StepSixForm from './steps-form/step-six-form'
import ProfileFormProgressBar from '../progress-bar'
    
function IndividualProfileForm() {
    return (
        <div className='flex flex-col gap-6'>
            {/* Step progress indicator and navigation header */}
            <RenderStepsHeader stepsHeading={INDIVIDUAL_STEPS_HEADING}  />
            <FormSteps />
        </div>
    )
}

function FormSteps() {
    const { currentStep } = useFormSteps()

    switch (currentStep) {
        case 1:
            return <StepOneForm />
        case 2:
            return <StepTwoForm />
        case 3:
            return <StepThreeForm />
        case 4:
            return <StepFourForm />
        case 5:
            return <StepFiveForm />
        case 6:
            return <StepSixForm />
        default:
            return (
                <div className="text-center text-muted-foreground">
                    <h2>Invalid step</h2>
                </div>
            )
    }
}


export default IndividualProfileForm