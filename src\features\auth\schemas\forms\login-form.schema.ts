import { emailSchema, passwordSchema } from "@/lib/schemas/common.schema"
import { z } from "zod"

export const loginFormSchema = z.object({
    email: emailSchema,
    password: passwordSchema,
})

export const forgotPasswordFormSchema = z.object({
    email: emailSchema,
})

export const resetPasswordFormSchema = z.object({
    email: emailSchema,
    newPassword: passwordSchema,
    confirmPassword: passwordSchema,
}).refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmNewPassword"],
})

export type LoginFormSchema = z.infer<typeof loginFormSchema>
export type ForgotPasswordFormSchema = z.infer<typeof forgotPasswordFormSchema>
export type ResetPasswordFormSchema = z.infer<typeof resetPasswordFormSchema>