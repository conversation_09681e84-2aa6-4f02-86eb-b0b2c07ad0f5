import { Badge } from '@/shared/components/ui/badge';

interface ReviewStatusBadgeProps {
  status: string;
  onStatusChange?: (newStatus: string) => void;
}

const ReviewStatusBadge = ({ status }: ReviewStatusBadgeProps) => {
  const variants = {
    published: 'default',
    pending: 'secondary',
    flagged: 'destructive',
    hidden: 'outline'
  };

  return (
    <Badge variant={variants[status as keyof typeof variants] as any}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

export default ReviewStatusBadge;
