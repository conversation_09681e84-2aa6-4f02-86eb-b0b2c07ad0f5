"use client"

import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { FormProvider, useForm, SubmitHandler, FieldValues, DefaultValues, UseFormProps, UseFormReturn } from 'react-hook-form';
import z from 'zod';


interface BaseFormProps<T extends FieldValues> {
    children: React.ReactNode | ((form: UseFormReturn<T>) => React.ReactNode);
    schema: z.ZodType<T, unknown, any>;
    defaultValues?: DefaultValues<T>;
    onSubmit: SubmitHandler<T>;
    formOptions?: UseFormProps<T>;
    formClassName?: string,
}


function BaseForm<T extends FieldValues>({
    children,
    schema,
    defaultValues,
    onSubmit,
    formOptions,
    formClassName,
}: BaseFormProps<T>) {

    const form = useForm<T>({
        resolver: zodResolver(schema),
        mode: "onSubmit",
        defaultValues,
        ...formOptions,
    });

    return (
        <FormProvider {...form}>
            <form
                className={cn('w-full', formClassName)}
                onSubmit={form.handleSubmit(onSubmit)}
                noValidate
            >
                {typeof children === 'function' ? children(form) : children}
            </form>
        </FormProvider>
    );
}

export default BaseForm