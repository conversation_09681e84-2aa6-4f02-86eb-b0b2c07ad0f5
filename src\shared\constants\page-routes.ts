import { AllRoutes } from "@/shared/types/utility.type"

const BASE_CRM_ROUTE = "/crm"
const BASE_LISTINGS_ROUTE = "/listings"
const BASE_ADS_MANAGER_ROUTE = "/ads-manager"
const BASE_BUSINESS_MANAGEMENT_ROUTE = "/business-management"
const BASE_HELP_ROUTE = "/help"

/**
 * All the available routes for the application
 */
export const PAGE_ROUTES = {
    home: "/",
    login: "/login",
    register: "/register",
    completeProfile: "/complete-profile",
    otpVerification: "/otp-verification",
    passwordOtpVerification: "/password-otp-verification",
    resetPassword: "/reset-password",
    dashboard: "/dashboard",
    forgotPassword: "/forgot-password",
    crm: {
        leads: `${BASE_CRM_ROUTE}/leads`,
        chats: `${BASE_CRM_ROUTE}/chats`,
        reviews: `${BASE_CRM_ROUTE}/reviews`,
        invoices: `${BASE_CRM_ROUTE}/invoices`,
    },
    listings: {
        properties: `${BASE_LISTINGS_ROUTE}/properties`,
        projects: `${BASE_LISTINGS_ROUTE}/projects`,
        services: `${BASE_LISTINGS_ROUTE}/services`,
        events: `${BASE_LISTINGS_ROUTE}/events`,
        portfolio: `${BASE_LISTINGS_ROUTE}/portfolio`,
        aiAgents: `${BASE_LISTINGS_ROUTE}/ai-agents`,

    },
    adsManager: {
        adsManager: `${BASE_ADS_MANAGER_ROUTE}/ads-manager`,
        adsPerformance: `${BASE_ADS_MANAGER_ROUTE}/ads-performance`,
    },
    businessManagement: {
        profile: `${BASE_BUSINESS_MANAGEMENT_ROUTE}/profile`,
        team: `${BASE_BUSINESS_MANAGEMENT_ROUTE}/team`,
        subscription: `${BASE_BUSINESS_MANAGEMENT_ROUTE}/subscription`,
        documents: `${BASE_BUSINESS_MANAGEMENT_ROUTE}/documents`,
        payments: `${BASE_BUSINESS_MANAGEMENT_ROUTE}/payments`,
    },
    help: {
        help: `${BASE_HELP_ROUTE}/help`,
    },
    profile: "/profile",
    properties: `/properties`
} as const

/**
 * Type to get all routes for the application
 */
export type StaticRoute = AllRoutes<typeof PAGE_ROUTES>