import { AlertTriangle } from 'lucide-react';
import { TReview } from '../types/review.type';
import ReviewCard from './review-card';

interface ReviewsCardsListProps {
  reviews: TReview[];
  filteredReviews: TReview[];
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  onViewDetails: (id: string) => void;
  onAddReview: () => void;
  onAddNote?: (id: string, note: string) => void;
  onReply?: (id: string) => void;
}

const ReviewsCardsList = ({
  reviews,
  filteredReviews,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  onViewDetails,
  onAddReview,
  onAddNote,
  onReply,
}: ReviewsCardsListProps) => {
  if (filteredReviews.length === 0) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No reviews found</h3>
        <p className="text-muted-foreground">Try adjusting your filters or search terms.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {filteredReviews.map((review) => (
        <ReviewCard
          key={review.id}
          review={review}
          onStatusChange={onStatusChange}
          onFlag={onFlag}
          onRestore={onRestore}
          onHide={onHide}
          onPublish={onPublish}
          onViewDetails={onViewDetails}
          onAddReview={onAddReview}
          onAddNote={onAddNote}
          onReply={onReply}
        />
      ))}
    </div>
  );
};

export default ReviewsCardsList;
