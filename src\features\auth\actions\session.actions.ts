"use server"

import API_ENDPOINTS from "@/shared/constants/api-routes"
import { executeApiRequest } from "@/lib/helpers/actions.helpers"
import { getAuthCookie } from "@/lib/helpers/cookies.helpers"
import { REQUEST_TYPE } from "@/shared/types/action-helper.type"
import { ApiResponse } from "@/shared/types/utility.type"
import * as jose from "jose"

type SessionToken = {
    id: number
    email: string
    exp: number
    iat: number
}

/**
 * Get the session from the cookie
 * @returns The session data or null if no session is found
 */
export async function getSession(): Promise<ApiResponse> {
    const session = await getAuthCookie()
    if (!session) {
        return {
            success: false,
            message: "Unauthorized",
            status: 401,
        }
    }
    const decoded = jose.decodeJwt<SessionToken>(session.value)
    // Check code expiration
    if (decoded.exp && decoded.exp < Date.now() / 1000) {
        return {
            success: false,
            message: "Unauthorized",
            status: 401,
        }
    }
    const userId = decoded.id
    const sessionData = await getSessionFromApi(userId)
    return sessionData
}

export async function verifyCookie(): Promise<null | SessionToken> {
    const session = await getAuthCookie()
    if(!session) return null
    const decoded = jose.decodeJwt<SessionToken>(session.value)
    if (decoded.exp && decoded.exp < Date.now() / 1000) {
        return null
    }
    return decoded
}


export async function getSessionFromApi(id: number): Promise<ApiResponse> {
    const res = await executeApiRequest({
        method: REQUEST_TYPE.get,
        url: `${API_ENDPOINTS.GET_SESSION}/${id}`,
        withAuth: true,
    })
    return res
}