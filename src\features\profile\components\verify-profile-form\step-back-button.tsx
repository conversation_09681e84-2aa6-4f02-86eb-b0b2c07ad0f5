import { But<PERSON> } from '@/components/ui/button'
import { useFormSteps } from '@/shared/context/form-steps-context'
import { ArrowLeft } from 'lucide-react'
import React from 'react'

function StepBackButton() {
    const { goToPreviousStep, hasPreviousStep } = useFormSteps()
    if(!hasPreviousStep) return null
    return (
        <div className='absolute left-0'>
            <Button 
                type='button' 
                variant={"muted"} 
                size={"icon"} 
                onClick={goToPreviousStep} 
                className='text-foreground/70 rounded-full'
                disabled={!hasPreviousStep}
            >
                <ArrowLeft />
            </Button>
        </div>
    )
}

export default StepBackButton