import SectionHeading from '@/shared/components/section-heading'
import SurfaceCard from '@/shared/components/surface-card'
import { Button } from '@/shared/components/ui/button'
import { Separator } from '@/shared/components/ui/separator'
import { ShieldIcon } from 'lucide-react'
import React from 'react'

function AccountOverviewInfo() {
    return (
        <SurfaceCard 
            className='flex flex-col gap-5'
            heading='Account Overview'
            headingProps={{
                LeftIcon: ShieldIcon,
            }}
        >
            <div className='grid grid-cols-1 gap-4'>
                {accountOverviewInfoItems.map((item) => (
                    <AccountOverviewInfoItem key={item.label} {...item} />
                ))}
            </div>
            <Separator />
            <Button variant={"outline"}>
                Change Password
            </Button>
            {/* TODO: Add a activate account */}
            <Button variant={"destructive"}>
                De-active Account
            </Button>
        </SurfaceCard>
    )
}


const accountOverviewInfoItems = [
    {
        label: 'Account Status',
        value: 'Active'
    },
    {
        label: 'Subscription',
        value: 'Pro'
    },
    {
        label: 'Member Since',
        value: 'January 2024'
    },
    {
        label: 'Location',
        value: 'Abu Dhabi'
    },
    {
        label: 'Workplace',
        value: 'Premium Real Estate Group'
    },
    {
        label: 'Industry',
        value: 'Real Estate'
    },
    {
        label: 'Total Listings',
        value: '108'
    },
    {
        label: 'Last Login',
        value: 'Today, 9: 30 AM'
    }
] as const

interface AccountOverviewInfoItemProps {
    label: string
    value: string
}

function AccountOverviewInfoItem(props: AccountOverviewInfoItemProps) {
    const { label, value } = props
    return (
        <div className='flex flex-col gap-0.5'>
            <p className='text-sm text-muted-foreground'>{label}</p>
            <p className='text-sm font-medium'>{value}</p>
        </div>
    )
}

export default AccountOverviewInfo