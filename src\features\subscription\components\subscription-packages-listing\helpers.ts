import { SubscriptionPackage } from "./data";

export type Props = {
  activeTab: "month" | "year";
  setActiveTab: (t: "month" | "year") => void;
};

export const tabs = [
  { id: "month", label: "BILLED MONTHLY" },
  { id: "year", label: "BILLED ANNUALLY" },
] as const;

export function getBadgeValue(pkg: SubscriptionPackage) {
  const badgeFeature = pkg?.features?.find(
    (f) => f.featureConstant.toLowerCase().trim() === "badge"
  );

  const v = badgeFeature?.featureValue ?? pkg.badge ?? "";
  return typeof v === "string" || typeof v === "number" ? String(v) : "";
}

export function isDowngrade(
  target: SubscriptionPackage,
  active?: SubscriptionPackage | null
) {
  if (!active) return false;
  const t = Number(target.price);
  const a = Number(active.price);
  return t < a && target.packageId !== active.packageId;
}
