import { Search } from 'lucide-react';

interface ReviewsFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedFilter: string;
  onFilterChange: (value: string) => void;
}

const ReviewsFilters = ({
  searchTerm,
  onSearchChange,
  selectedFilter,
  onFilterChange,
}: ReviewsFiltersProps) => {
  return (
    // <div className="bg-white rounded-lg shadow">
      <div className="flex gap-4 p-6 ">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            placeholder="Search reviews..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 py-2 border border-gray-300 rounded-md w-full"
          />
        </div>
        <select
          value={selectedFilter}
          onChange={(e) => onFilterChange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Filter reviews"
        >
          <option value="all">All Reviews</option>
          <option value="replied">Replied</option>
          <option value="unreplied">Unreplied</option>
          <option value="flagged">Flagged</option>
          <option value="published">Published</option>
          <option value="pending">Pending</option>
          <option value="hidden">Hidden</option>
          <option value="high-rating">High Rating (4-5★)</option>
          <option value="low-rating">Low Rating (1-2★)</option>
        </select>
      </div>
    // </div>
  );
};

export default ReviewsFilters;
