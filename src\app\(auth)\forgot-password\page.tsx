import { PAGE_ROUTES } from '@/constants/page-routes'
import AuthCardWrapper from '@/auth/components/auth-card-wrapper'
import AuthRedirectText from '@/auth/components/auth-redirect-text'
import ForgotPasswordForm from '@/auth/components/forms/forgot-password-form'
import React from 'react'

function ForgotPasswordPage() {
    return (
        <AuthCardWrapper
            title="Forgot Password"
            description="Enter your email to reset your password"
        >
            <ForgotPasswordForm />
            <AuthRedirectText
                prompt="Remember your password?"
                linkText="Login"
                href={PAGE_ROUTES.login}
            />
        </AuthCardWrapper>
    )
}

export default ForgotPasswordPage