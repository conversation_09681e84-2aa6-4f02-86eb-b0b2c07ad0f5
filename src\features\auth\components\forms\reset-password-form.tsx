"use client"

import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'
import { ResetPasswordFormSchema, resetPasswordFormSchema } from '@/auth/schemas/forms/login-form.schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { resetPassword } from '@/auth/actions/auth.action'
import { TextInput } from '@/components/form-inputs/text-input'
import { handleApiResponse } from '@/lib/helpers/client.helpers'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { Button } from '@/components/ui/button'
import { getUserTempEmail, removeUserTempEmail } from '@/lib/helpers/local-storage.helpers'

function ResetPasswordForm() {
    const router = useRouter()

    // Initializing the reset password form
    const form = useForm<ResetPasswordFormSchema>({
        resolver: zodResolver(resetPasswordFormSchema),
        defaultValues: {
            email: "",
            newPassword: "",
            confirmPassword: "",
        },
    })

    // Handle the form submission
    const onSubmit = async (data: ResetPasswordFormSchema) => {
        const response = await resetPassword(data)
        handleApiResponse({
            response,
            successMessage: "Password reset successfully",
            onSuccess: () => {
                removeUserTempEmail()
                router.push(PAGE_ROUTES.login)
            },
        })
    }

    // This is used to get the email from the local storage,
    // if the email is not found, then redirect to the register page,
    // else set the email to the email field in the form
    useEffect(() => {
        const email = getUserTempEmail()
        if (email) {
            form.setValue('email', email)
        } else {
            router.push(PAGE_ROUTES.login)
        }
    }, [])

    return (
        <FormProvider {...form}>
            <form 
                className='flex flex-col gap-6 w-full'
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <TextInput 
                    name="newPassword" 
                    label="New Password" 
                    control={form.control}
                    type='password'
                />
                <TextInput 
                    name="confirmPassword" 
                    label="Confirm New Password" 
                    control={form.control}
                    type='password'
                />
                <Button 
                    type='submit'
                    isLoading={form.formState.isSubmitting}
                >
                    Reset Password
                </Button>
            </form>
        </FormProvider>
    )
}

export default ResetPasswordForm