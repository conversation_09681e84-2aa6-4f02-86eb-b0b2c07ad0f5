import { VariantProps } from "class-variance-authority";
import { PropertyListing } from "./data";
import { badgeVariants } from "@/shared/components/ui/badge";

export const returnVariantForStatus = (status: PropertyListing["status"]): VariantProps<typeof badgeVariants>["variant"] => {
    switch (status) {
        case "Draft": return "muted-soft";
        case "Sold": return "destructive-soft";
        case "Available": return "success-soft";
        case "Unpublished": return "muted-soft";
        case "Rented": return "warning-soft"
    }
}