"use server"

import API_ENDPOINTS from "@/shared/constants/api-routes"
import { executeApiRequest } from "@/shared/lib/helpers/actions.helpers"
import { REQUEST_TYPE } from "@/shared/types/action-helper.type"
import { ApiResponse } from "@/shared/types/utility.type"
import { ApiProfileDetails } from "../types/profile.type"

/**
 * Get Profile Info.
 * This action is used to get the profile info of the user.
 */
export async function getProfileInfo(): Promise<ApiResponse<ApiProfileDetails>> {
    const response = await executeApiRequest<ApiProfileDetails>({
        method: REQUEST_TYPE.get,
        url: API_ENDPOINTS.GET_PROFILE_DETAILS,
        withAuth: true,
        options: {
            cache: "force-cache",
        }
    })
    return response
}