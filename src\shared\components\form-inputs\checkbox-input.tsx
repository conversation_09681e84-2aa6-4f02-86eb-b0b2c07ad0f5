"use client"

import { Control, FieldPath, FieldValues } from "react-hook-form"

import { Checkbox } from "@/components/ui/checkbox"
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
} from "@/ui/form"
import { SelectOptions } from "@/shared/types"
import FormGroup from "./form-group"
import { FormGroupPropsWithoutChildrenAndIcons } from "@/shared/types/inputs/form-group.type"
import { cn } from "@/lib/utils"


interface CheckboxInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends FormGroupPropsWithoutChildrenAndIcons {
    name: TName,
    control: Control<TFieldValues>,
    options: SelectOptions,
    optionsContainerClassName?: string,
    optionLabelClassName?: string,
}

export function CheckboxInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    name,
    control,
    options,
    optionsContainerClassName,
    optionLabelClassName,
    ...formGroupProps
}: CheckboxInputProps<TFieldValues, TName>) {


    return (
        <FormField
            control={control}
            name={name}
            render={() => (
                <FormGroup {...formGroupProps}>
                    <div className={cn("flex flex-col gap-5 w-full", optionsContainerClassName)}>
                        {options.map((item) => (
                            <FormField
                                key={item.value}
                                control={control}
                                name={name}
                                render={({ field }) => {
                                    return (
                                        <FormItem
                                            key={item.value}
                                            className={cn("flex flex-row items-center space-y-0 gap-0")}
                                        >
                                            <FormControl>
                                                <Checkbox
                                                    checked={field.value?.includes(item.value)}
                                                    onCheckedChange={(checked) => {
                                                        return checked
                                                            ? field.onChange([...field.value, item.value])
                                                            : field.onChange(
                                                                field.value?.filter(
                                                                    (value: string) => value !== item.value
                                                                )
                                                            )
                                                    }}
                                                />
                                            </FormControl>
                                            <FormLabel className={cn("font-normal text-foreground text-sm pl-2 cursor-pointer", optionLabelClassName)}>
                                                {item.label}
                                            </FormLabel>
                                        </FormItem>
                                    )
                                }}
                            />
                        ))}
                    </div>
                </FormGroup>
            )}
        />
    )
}
