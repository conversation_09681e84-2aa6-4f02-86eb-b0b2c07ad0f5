/**
 * Props interface for the FormGroup component
 */
export interface FormGroupProps {
    /** Optional label text to display above the input */
    label?: string;
    /** Optional CSS classes to apply to the wrapper container */
    inputGroupClassName?: string;
    /** Optional CSS classes to apply to the label element */
    labelClassName?: string;
    /** The input component or form control to render inside the group */
    children: React.ReactNode;
    /** Optional classes to apply to the error message */
    errorMessageClassName?: string;
}

/**
 * Utility type that excludes the children prop from FormGroupProps
 * Useful for cases where you need FormGroup props but will provide children separately
 */
export type FormGroupPropsWithoutChildren = Omit<FormGroupProps, "children">

/**
 * Form group props without children, leftIcon and rightIcon
 */
export type FormGroupPropsWithoutChildrenAndIcons = Omit<FormGroupPropsWithoutChildren, "leftIcon" | "rightIcon">