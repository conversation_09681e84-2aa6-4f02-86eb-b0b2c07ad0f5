"use client";

import { CircleX, <PERSON>Upload, X } from "lucide-react";
import { Control, ControllerRenderProps, FieldPath, FieldValues, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
    FormField,
    useForm<PERSON>ield,
} from "@/components/ui/form";
import {
    FileUpload,
    FileUploadDropzone,
    FileUploadItem,
    FileUploadItemDelete,
    FileUploadItemMetadata,
    FileUploadItemPreview,
    FileUploadList,
} from "@/components/ui/file-upload";
import FormGroup from "../form-group";
import { FormGroupPropsWithoutChildrenAndIcons } from "@/shared/types/inputs/form-group.type";
import { cn } from "@/shared/lib/utils";
import { className as inputClassName } from "@/shared/components/ui/input";
import { INPUT_IMAGE_SIZE } from "@/shared/constants/input-image-size";


interface FileUploadInputProps<
    TFieldValues extends FieldValues = FieldVal<PERSON>,
    TN<PERSON> extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends FormGroupPropsWithoutChildrenAndIcons {
    name: TName
    control: Control<TFieldValues>
    accept?: string
    maxFiles?: number
    maxSize?: number
    multiple?: boolean
}

export function FileUploadInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    name,
    control,
    maxSize = INPUT_IMAGE_SIZE.PROFILE_PHOTO,
    maxFiles = 6,
    ...formGroupProps
}: FileUploadInputProps<TFieldValues, TName>) {

    const { setError } = useFormContext()
    const multiple = maxFiles > 1 ? true : false
    return (
        <FormField
            control={control}
            name={name}
            render={({ field }) => (
                <FormGroup {...formGroupProps}>
                    <FileUpload
                        value={field.value}
                        onValueChange={field.onChange}
                        accept="image/*"
                        maxFiles={maxFiles}
                        disabled={field.disabled}
                        maxSize={maxSize}
                        onFileReject={(file, message) => {
                            if (file.size > maxSize) {
                                setError(name, {
                                    message: `Maximum file size is ${maxSize / 1024 / 1024}MB`
                                })
                                return
                            }
                            setError(name, {
                                message: message
                            })
                        }}
                        multiple={multiple}
                        className="w-full"
                    >
                        {(!field.value || field.value.length < maxFiles) && (
                            <BaseFileUploadInput field={{...field}} />
                        )}
                        {/* {!multiple && (
                            <BaseFileUploadInput field={{...field}} />
                        )} */}
                        <FileUploadList>
                            {field?.value?.map((file: File, index: number) => (
                                <FileUploadItem
                                    key={`${index}-${file.name}-${file.size}`}
                                    value={file}
                                >
                                    <FileUploadItemPreview />
                                    <FileUploadItemMetadata />
                                    <FileUploadItemDelete asChild>
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            className="size-5 text-muted-foreground/50"
                                        >
                                            <CircleX />
                                            <span className="sr-only">Delete</span>
                                        </Button>
                                    </FileUploadItemDelete>
                                </FileUploadItem>
                            ))}
                        </FileUploadList>
                    </FileUpload>
                </FormGroup>
            )}
        />
    )
}

type BaseFileUploadInputProps = {
    field: ControllerRenderProps<FieldValues, string>,
}

const BaseFileUploadInput = ({ field }: BaseFileUploadInputProps) => {
    const { error } = useFormField()
    return (
        <FileUploadDropzone
            aria-invalid={!!error}
            ref={field.ref}
            className={cn(
                inputClassName,
                "flex-row flex-wrap text-center text-muted-foreground/70 cursor-pointer"
            )}
        >
            <CloudUpload className="size-5" />
            <span className="text-sm">
                Drag and drop or to upload file
            </span>
        </FileUploadDropzone>
    )
}