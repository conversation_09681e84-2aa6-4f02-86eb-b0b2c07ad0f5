import { USER_TEMP_EMAIL } from "@/constants/local-storage-keys"

/**
 * This function is used to get the user's temporary email from the local storage
 * @returns The user's temporary email
 */
export function getUserTempEmail() {
    const userTempEmail = localStorage.getItem(USER_TEMP_EMAIL)
    if (!userTempEmail) {
        return null
    }
    return userTempEmail
}

/**
 * This function is used to set the user's temporary email in the local storage
 * @param email The user's temporary email
 */
export function setUserTempEmail(email: string) {
    localStorage.setItem(USER_TEMP_EMAIL, email)
}

/**
 * This function is used to remove the user's temporary email from the local storage,
 * If email is not provided, it will remove the user's temporary email from the local storage
 * @param email The user's temporary email
 */
export function removeUserTempEmail(email?: string) {
    localStorage.removeItem(email || USER_TEMP_EMAIL)
}