import { ApiIndustryOption, ApiIndustryOptionValue, ApiIndustryRoleOption } from "../types/profile-roles.type"



/**
 * Stringify the IndustryOption into a string
 * @param option - The industry option to stringify
 * @returns The stringified industry option
 * Example: 1-Real Estate-123
 */
export function stringifyIndustryOption(option: ApiIndustryOptionValue) {
    return `${option.id}-${option.name}`
}


/**
 * Parse the stringified industry option into an IndustryOption
 * @param option - The stringified industry option
 * @returns The parsed industry option
 * Example: Converts "1-Real Estate" into { id: 1, name: "Real Estate" }
 */
export function parseIndustryOption(option: string) {
    // Check for validation that option is correct string or not
    const [id, name] = option.split("-")
    return {
        id: Number(id),
        name,
    }
}


/**
 * Stringify the IndustryRoleOption into a string
 * @param role - The industry role option to stringify
 * @returns The stringified industry role option
 * Example: 1-Real Estate-123
 */
export function stringifyIndustryRoleOption(role: ApiIndustryRoleOption) {
    return `${role.id}-${role.name}`
}


/**
 * Parse the stringified industry role option into an IndustryRoleOption
 * @param role - The stringified industry role option
 * @returns The parsed industry role option
 * Example: { id: 1, name: "Real Estate", parentId: 123 }
 */
export function parseIndustryRoleOption(role: string) {
    const [id, name] = role.split("-")
    return {
        id: Number(id),
        name,
    }
}



export function stringifyIndustryAndRole(industry: ApiIndustryOptionValue, role: ApiIndustryRoleOption) {
    return `${stringifyIndustryOption(industry)}|${stringifyIndustryRoleOption(role)}`
}

export function parseIndustryAndRole(industryAndRole: string) {
    const [industry, role] = industryAndRole.split("__")
    return {
        industry: parseIndustryOption(industry),
        role: parseIndustryRoleOption(role),
    }
}


// export function parseIndustryAndRole(industryAndRole: string) {
//     const [industryId, industryName, roleId, roleName] = industryAndRole.split("-")
//     return {
// }