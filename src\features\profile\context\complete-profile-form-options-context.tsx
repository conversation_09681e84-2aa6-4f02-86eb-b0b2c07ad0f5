"use client"

import { Location } from "@/profile/types/location.type";
import { Nationality } from "@/profile/types/nationality.type";
import { 
    createContext, 
    Dispatch, 
    PropsWithChildren, 
    SetStateAction, 
    useContext, 
    useState 
} from "react";
import { ApiIndustryOption, ApiIndustryRoleOption } from "../types/profile-roles.type";


type CompleteProfileFormOptions = {
    nationalities: Nationality[],
    locations: Location[],
    industries: ApiIndustryOption[],
    roles: Record<string, ApiIndustryRoleOption[]>,
    setRoles: Dispatch<SetStateAction<Record<string, ApiIndustryRoleOption[]>>>,
}

export const CompleteProfileFormOptionsContext = createContext<CompleteProfileFormOptions>({
    nationalities: [],
    locations: [],
    industries: [],
    roles: {},
    setRoles: () => { },
})

export function CompleteProfileFormOptionsProvider({
    children,
    nationalities,
    locations,
    industries,
}: PropsWithChildren<Omit<CompleteProfileFormOptions, "roles" | "setRoles">>) {
    const [roles, setRoles] = useState<Record<string, ApiIndustryRoleOption[]>>({})
    return (
        <CompleteProfileFormOptionsContext.Provider
            value={{
                nationalities,
                locations,
                industries,
                roles,
                setRoles,
            }}
        >
            {children}
        </CompleteProfileFormOptionsContext.Provider>
    )
}

export function useCompleteProfileFormOptions() {
    const context = useContext(CompleteProfileFormOptionsContext)
    return context
}