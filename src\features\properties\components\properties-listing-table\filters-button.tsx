import { SelectInput } from '@/shared/components/form-inputs/select-input'
import { Button } from '@/shared/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/shared/components/ui/dialog'
import { PROPERTY_STATUSES_OPTIONS, PROPERTY_TYPES_OPTIONS } from '@/shared/constants/select-options'
import { Filter } from 'lucide-react'
import { ChevronDown } from 'lucide-react'
import React, { useState } from 'react'
import { useFormContext } from 'react-hook-form'

function FiltersButton() {
    const form = useFormContext()
    const [isOpen, setIsOpen] = useState(false)

    const handleClearFilters = () => {
        form.resetField('status', { defaultValue: "" })
        form.resetField('location', { defaultValue: "" })
        form.resetField('type', { defaultValue: "" })
    }

    const handleApplyFilters = () => {
        setIsOpen(false)
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="sm" className='h-9'>
                    <Filter />
                    <span className='hidden md:block'>Filters</span>
                </Button>               
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Property Filters</DialogTitle>
                    <DialogDescription className='hidden'>
                        Filter properties by status, location, and type.
                    </DialogDescription>
                </DialogHeader>
                <div className="flex gap-3 flex-col">
                    <SelectInput
                        control={form.control}
                        name="status"
                        options={PROPERTY_STATUSES_OPTIONS}
                        placeholder="Select status"
                        // label='Status'
                    />
                    <SelectInput
                        control={form.control}
                        name="location"
                        options={[]}
                        placeholder="Select location"
                        // label='Location'
                    />
                    <SelectInput
                        control={form.control}
                        name="type"
                        options={PROPERTY_TYPES_OPTIONS}
                        placeholder="Select type"
                        // label='Type'
                    />
                </div>
                <DialogFooter className='pt-1'>
                    <Button 
                        variant="outline" 
                        onClick={handleClearFilters} 
                        type='button'
                        disabled={!form.formState.isDirty}
                    >
                        Clear Filters
                    </Button>
                    <Button 
                        onClick={handleApplyFilters} 
                        type='button'
                        disabled={!form.formState.isDirty}
                    >
                        Apply Filters
                    </Button>
                </DialogFooter> 
            </DialogContent>
        </Dialog>
    )
}

export default FiltersButton