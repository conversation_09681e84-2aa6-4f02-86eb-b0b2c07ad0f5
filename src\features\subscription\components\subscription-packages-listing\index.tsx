"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import PackageCard from "./package-card";
import CancelSubscriptionButton from "./cancel-subscription-button";
import SubscriptionCancelPopup from "./subscription-cancel-popup";
import FiltersButton from "./filters-button";
import {
  SubscriptionPackage,
  getPackagesData,
  subscribeToPackage,
  cancelSubscription,
} from "./data";
import { getBadgeValue, isDowngrade } from "./helpers";
import API_ENDPOINTS from "@/shared/constants/api-routes";
import { errorToast, successToast } from "@/shared/lib/toast";
import Loading from "@/app/(dashboard)/loading";
import PackageDowngradeModal from "./PackageDowngradeModal";

const SubscriptionPackages = () => {
  const router = useRouter();
  const [activePackage, setActivePackage] = useState<string | null>("Basic");
  const [activePackageId, setActivePackageId] = useState<number | null>(null);
  const [subscriptionId, setSubscriptionId] = useState<number | null>(null);
  const [packages, setPackages] = useState<SubscriptionPackage[]>([]);
  const [settings, setSettings] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<"month" | "year">("month");
  const [showCancelButton, setShowCancelButton] = useState(true);
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showDowngradeModal, setShowDowngradeModal] = useState(false);
  const [downgradeTarget, setDowngradeTarget] =
    useState<SubscriptionPackage | null>(null);

  const load = useCallback(async () => {
    try {
      setLoader(true);
      const result = await getPackagesData(API_ENDPOINTS.SUBSCRIPTION_PACKAGES);
      if (result?.success) {
        setSettings(result?.data?.settings || null);
        const all = result?.data?.packages as SubscriptionPackage[];
        const active = all.find((pkg) => pkg.isActive);
        setSubscriptionId(active?.subscriptionId ?? null);
        setPackages(all);
        setActivePackageId(active?.packageId ?? null);
        setActivePackage(active?.name || "Basic");
      }
    } catch (e) {
      console.error(e);
    } finally {
      setLoader(false);
    }
  }, []);

  useEffect(() => {
    load();
  }, [load]);

  useEffect(() => {
    let id: NodeJS.Timeout;
    if (showCancelPopup) id = setTimeout(() => setShowCancelPopup(false), 5000);
    return () => id && clearTimeout(id);
  }, [showCancelPopup]);

  const handleSubscribePackage = async (id: number) => {
    try {
      setLoader(true);
      const { ok, result } = await subscribeToPackage(
        API_ENDPOINTS.SUBSCRIPTION_PACKAGES,
        id
      );

      if (!ok) throw new Error(result.message || "Failed to subscribe");

      if (result?.success) {
        if (result?.data?.noRedirect) {
          setLoader(false);
          successToast(
            result?.data?.message || "Subscription completed successfully!"
          );
          await load();
          return;
        } else {
          router.push(result?.data?.sessionUrl);
        }
      }
    } catch (err) {
      console.error("API error:", err);
    } finally {
      setLoader(false);
    }
  };

  const handleCancelSubscribePackage = async () => {
    try {
      setLoader(true);
      if (!subscriptionId) {
        errorToast("Active package Id is required");
        return;
      }
      const { ok, result } = await cancelSubscription(
        API_ENDPOINTS.SUBSCRIPTION_PACKAGES,
        subscriptionId
      );
      if (!ok)
        throw new Error(result.message || "Failed to cancel subscription");

      if (result.success) {
        successToast(result?.message || "Subscription cancelled successfully!");
        await load();
        setActivePackage("Basic");
        setShowCancelButton(false);
        setShowCancelPopup(true);
      } else {
        errorToast(result?.message || "Failed to cancel subscription");
      }
    } catch (err) {
      console.error("API error:", err);
    } finally {
      setLoader(false);
    }
  };

  const filteredPackages = packages.filter((pkg) => pkg.interval === activeTab);

  return (
    <>
      {loader && <Loading />}
      {!loader && (
        <div className="w-full py-6">
          {showCancelButton && activePackage && activePackage !== "Basic" && (
            <CancelSubscriptionButton onCancel={handleCancelSubscribePackage} />
          )}

          {/* Tabs (same design) */}
          <FiltersButton activeTab={activeTab} setActiveTab={setActiveTab} />

          {/* Package Grid (same design) */}
          <div className="grid grid-cols-1 gap-6 px-7 py-4 md:grid-cols-2 lg:grid-cols-4">
            {filteredPackages.map((pkg) => {
              const badgeValue = getBadgeValue(pkg);
              const activePkg = packages.find(
                (p) => p.packageId === activePackageId
              );
              const isCurrent = pkg.packageId === activePackageId;
              const willDowngrade = isDowngrade(pkg, activePkg ?? null);

              let buttonText = "Upgrade";
              if (isCurrent) buttonText = "Current Plan";
              else if (willDowngrade) buttonText = "Downgrade";

              return (
                <PackageCard
                  key={pkg.name}
                  name={pkg.name}
                  price={pkg.price}
                  discount={pkg.discount}
                  badge={String(badgeValue)}
                  currency={pkg.currency}
                  isActive={isCurrent}
                  features={pkg.features}
                  buttonText={buttonText}
                  colorTheme={pkg.colorTheme}
                  onSubscribe={() => {
                    if (willDowngrade) {
                      setDowngradeTarget(pkg);
                      setShowDowngradeModal(true);
                    } else {
                      handleSubscribePackage(pkg.packageId);
                    }
                  }}
                />
              );
            })}
          </div>

          <SubscriptionCancelPopup isVisible={showCancelPopup} />
        </div>
      )}

      {showDowngradeModal && downgradeTarget && (
        <PackageDowngradeModal
          isOpen={showDowngradeModal}
          onClose={() => setShowDowngradeModal(false)}
          onConfirm={() => handleSubscribePackage(downgradeTarget.packageId)}
          current={packages.find((p) => p.packageId === activePackageId)!}
          target={downgradeTarget}
        />
      )}
    </>
  );
};

export default SubscriptionPackages;
