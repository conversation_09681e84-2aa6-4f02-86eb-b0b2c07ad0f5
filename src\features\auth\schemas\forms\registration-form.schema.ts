import { emailSchema, passwordSchema, selectSchema } from "@/lib/schemas/common.schema"
import { registrationFormOptions } from "@/auth/constants/input-options"
import { z } from "zod"

export const registrationFormSchema = z.object({
    accountType: selectSchema(registrationFormOptions),
    email: emailSchema,
    password: passwordSchema,
    confirmPassword: passwordSchema,
}).refine((data) => data.password === data.confirmPassword, {
    path: ["confirmPassword"],
    message: "Passwords do not match",
})

export const otpVerificationFormSchema = z.object({
    otp: z.string().min(4, { message: "OTP must be 4 digits" }),
    email: emailSchema,
})

export const resendOtpSchema = z.object({
    email: emailSchema,
})

export type RegistrationFormSchema = z.infer<typeof registrationFormSchema>
export type OtpVerificationFormSchema = z.infer<typeof otpVerificationFormSchema>
export type ResendOtpSchema = z.infer<typeof resendOtpSchema>