import React from 'react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { FormControl, FormItem, useFormField } from '@/components/ui/form'
import { FormLabel } from '@/components/ui/form'
import { SelectOptions } from '@/shared/types'
import { ControllerRenderProps, FieldValues } from 'react-hook-form'

interface BaseRadioInput extends ControllerRenderProps<FieldValues, string> {
    options: SelectOptions,
}

function BaseRadioInput({
    options,
    name,
    onBlur,
    onChange,
    ref,
    value,
    disabled,
}: BaseRadioInput) {
    const { error } = useFormField()
    return (
        <RadioGroup
            className="flex flex-col gap-2 pt-2"
            value={value || ""}
            name={name}
            onBlur={onBlur}
            ref={ref}
            onValueChange={onChange}
            disabled={disabled}
            aria-invalid={!!error}
        >   
            {options.map(({ label, value }) => (
                <FormItem 
                    key={`radio-group-item-${value}`}
                    className="flex items-center gap-0" 
                >
                    <FormControl>
                        <RadioGroupItem value={value} />
                    </FormControl>
                    <FormLabel className="font-medium text-sm pl-2 cursor-pointer text-foreground">
                        {label}
                    </FormLabel>
                </FormItem>
            ))}
        </RadioGroup>
    )
}

export default BaseRadioInput