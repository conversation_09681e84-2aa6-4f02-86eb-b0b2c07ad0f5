import { FormGroupProps, FormGroupPropsWithoutChildren, FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import React from 'react'
import { Control, ControllerRenderProps, FieldPath, FieldValues } from 'react-hook-form'
import FormGroup from './form-group'
import { FormField } from '../ui/form'
import { Textarea } from '../ui/textarea'
import { cn } from '@/lib/utils'
import { className as baseInputClassName } from '@/components/ui/input'
import { BaseFormFieldProps } from '@/shared/types/inputs'

export interface TextareaInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends BaseFormFieldProps<TFieldValues, TName>, FormGroupPropsWithoutChildren {
    placeholder?: string,
    className?: string,
    disabled?: boolean,
    rows?: number,
}

function TextareaInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    control,
    name,
    placeholder,
    className,
    disabled,
    rows = 4,
    ...formGroupProps
}: TextareaInputProps<TFieldValues, TName>) {
    return (
        <FormField 
            control={control}
            name={name}
            render={({ field, fieldState }) => (
                <FormGroup {...formGroupProps}>
                    <Textarea
                        {...field}
                        placeholder={placeholder}
                        className={cn(
                            baseInputClassName, 
                            className
                        )}
                        disabled={disabled}
                        aria-invalid={fieldState.invalid}
                        rows={rows}
                    />
                </FormGroup>
            )}
        />
    )
}



export default TextareaInput