"use client"

import { SIDEBAR_LINKS } from '@/constants/sidebar-links'
import React from 'react'
import { SidebarMenuButton, SidebarMenuItem, useSidebar } from '../ui/sidebar'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

type DashboardSidebarLinkProps = (typeof SIDEBAR_LINKS)[number]["subLinks"][number]

function DashboardSidebarLink({ label, href, Icon }: DashboardSidebarLinkProps) {
    const pathname = usePathname()
    const isActive = pathname === href
    const { isMobile, toggleSidebar } = useSidebar()

    const handleLinkClick = () => {
        // Close mobile sidebar when a link is clicked on mobile
        if (isMobile) {
            toggleSidebar()
        }
    }

    return (
        <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={isActive}>
                <Link href={href} onClick={handleLinkClick}>
                    <Icon className="mr-2 h-4 w-4" />
                    <span>{label}</span>
                </Link>
            </SidebarMenuButton>
        </SidebarMenuItem>
    )
}

export default DashboardSidebarLink