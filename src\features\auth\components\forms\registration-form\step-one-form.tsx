import CardCheckboxInput from '@/components/form-inputs/card-checkbox-input'
import { RegistrationFormSchema } from '@/auth/schemas/forms/registration-form.schema'
import { useFormContext } from 'react-hook-form'
import React from 'react'
import { registrationFormOptions } from '@/auth/constants/input-options'
import StepOneNextButton from './step-one-next-button'
import AuthCardWrapper from '@/auth/components/auth-card-wrapper'

/**
 * Step One of the Registration Form
 * This is the form where the user can select their account type
 */
function StepOneForm() {
    const { control } = useFormContext<RegistrationFormSchema>()

    return (
        <AuthCardWrapper title="Who Are You Registering As?">
            <CardCheckboxInput
                control={control}
                name="accountType"
                options={registrationFormOptions}
            />
            <StepOneNextButton />
        </AuthCardWrapper>
    )
}

export default StepOneForm