"use client"

import React from 'react'
import { useFormSteps } from '@/shared/context/form-steps-context'
import StepHeader, { StepHeaderProps } from './step-header'

interface RenderStepsHeaderProps {
    stepsHeading: Record<number, StepHeaderProps>
}

/**
 * Complete Profile Form Steps Header,
 * This component show the steps header for each step of the complete profile form
 */
function RenderStepsHeader({ stepsHeading }: RenderStepsHeaderProps) {
    const { currentStep } = useFormSteps()
    const stepHeading = stepsHeading[currentStep]

    if(!stepHeading) return null

    return <StepHeader {...stepHeading} />
}

export default RenderStepsHeader
