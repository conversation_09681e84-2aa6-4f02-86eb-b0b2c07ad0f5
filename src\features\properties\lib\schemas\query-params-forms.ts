import { PROPERTY_STATUSES_OPTIONS, PROPERTY_TYPES_OPTIONS } from "@/shared/constants/select-options"
import { selectSchema } from "@/shared/lib/schemas/common.schema"
import { z } from "zod"

/** All sortable columns of the properties page */
export const SORT_FIELDS = [
    "property",
    "type",
    "location",
    "listing",
    "price",
    "postedBy",
    "status",
    "createdAt",
] as const;

/** All possible order values */
export const ORDER = ["asc", "desc"] as const;

/** Helper: accept either `"a,b,c"` or `["a","b","c"]` */
const csvOrArray = <T extends z.ZodTypeAny>(item: T) =>
    z.preprocess(
        (v) =>
            typeof v === "string"
                ? v.split(",").map((s) => s.trim()).filter(Boolean)
                : v,
        z.array(item),
    );

/**
 * Schema for the query params of the properties page
 */
export const propertyQueryParamsSchema = z
    .object({
        search: z.string().trim().optional(),
        status: selectSchema(PROPERTY_STATUSES_OPTIONS).default("all").optional(),
        type: selectSchema(PROPERTY_TYPES_OPTIONS).default("all").optional(),
        location: z.string().optional(),

        page: z.preprocess(Number, z.number().int().min(1)).default(1),
        limit: z.preprocess(Number, z.number().int().min(1)).default(10),

        /** ⇢  now **arrays** (comma-separated also works) */
        sort: csvOrArray(z.enum(SORT_FIELDS)).default(["createdAt"]),
        order: csvOrArray(z.enum(ORDER)).default(["desc"]),
    })
    /* order can be 1-element (applies to all) or same length as sort */
    .refine(
        ({ sort, order }) => order.length === 1 || order.length === sort.length,
        { message: "`order` must be length 1 or match `sort` length" },
    );