import { GENDER_OPTIONS } from '@/shared/constants/select-options'
import { emailSchema, fileSchema, selectSchema, textSchema } from '@/shared/lib/schemas/common.schema'
import { z } from 'zod'

export const profileInfoFormSchema = z.object({
    firstName: textSchema,
    lastName: textSchema,
    middleName: textSchema.optional(),
    nationality: textSchema,
    gender: selectSchema(GENDER_OPTIONS),
    profilePhoto: z.array(fileSchema.or(z.string())).optional(),
})

export const contactInfoFormSchema = z.object({
    email: emailSchema,
    contact: textSchema,
    whatsapp: textSchema,
    location: textSchema,
    address: textSchema,
})

export const professionalInfoFormSchema = z.object({
    workPlace: textSchema,
    industry: textSchema,
    roleType: textSchema,
    description: textSchema,
    specialization: z.array(z.object({value: textSchema})),
    languages: textSchema.optional(),
})