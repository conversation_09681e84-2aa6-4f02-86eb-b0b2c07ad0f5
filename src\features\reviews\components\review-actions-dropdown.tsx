import { Button } from '@/shared/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/components/ui/dropdown-menu';
import { MoreVertical, ArrowDownCircle, Trash2, Eye, Check, ArrowUpCircle, MessageSquare } from 'lucide-react';
import { TReview } from '../types/review.type';
import { successToast } from '@/shared/lib/toast';

interface ReviewActionsDropdownProps {
  review: TReview;
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  isDialog?: boolean;
  onViewDetails?: (id: string) => void;
  onAddReview?: () => void;
  onDelete?: (id: string) => void;
  onAddNote?: (id: string, note: string) => void;
}

const ReviewActionsDropdown = ({
  review,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  isDialog,
  onViewDetails,
  onDelete,
  onAddNote,
}: ReviewActionsDropdownProps) => {
  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(review.id);
    } else {
      successToast('View review details');
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(review.id);
    } else {
      successToast('Delete action not provided');
    }
  };

  const handleAddNote = () => {
    if (onAddNote) {
      const note = prompt('Enter your note:');
      if (note) {
        onAddNote(review.id, note);
      }
    } else {
      successToast('Add note functionality will be implemented soon');
    }
  };

  // Only show View, Hide, Delete for published
  if (review.status === 'published') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            size="sm" 
            variant={isDialog ? 'outline' : 'ghost'} 
            className={isDialog ? 'ml-2' : ''}
          >
            <MoreVertical className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="z-50 bg-white border">
          <DropdownMenuItem onClick={handleViewDetails}>
            <Eye className="w-4 h-4 mr-2" />
            View
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              const reason = prompt('Reason for hiding review?');
              if (reason) onHide(review.id, reason);
            }}
            className="text-red-600"
          >
            <ArrowDownCircle className="w-4 h-4 mr-2" />
            Hide Review
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-destructive"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </DropdownMenuItem>
          <div className="border-t my-1"></div>
          {onAddNote && (
            <DropdownMenuItem onClick={handleAddNote}>
              <MessageSquare className="w-4 h-4 mr-2" />
              Add Note
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Default: all other statuses keep previous logic
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          size="sm" 
          variant={isDialog ? 'outline' : 'ghost'} 
          className={isDialog ? 'ml-2' : ''}
        >
          <MoreVertical className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="z-50 bg-white border">
        <DropdownMenuItem onClick={handleViewDetails}>
          <Eye className="w-4 h-4 mr-2" />
          View
        </DropdownMenuItem>
        {/* Divider */}
        <div className="border-t my-1"></div>
        {review.status === 'hidden' && (
          <DropdownMenuItem
            onClick={() => onRestore(review.id)}
            className="text-green-700"
          >
            <ArrowUpCircle className="w-4 h-4 mr-2" />
            Restore & Publish
          </DropdownMenuItem>
        )}
        {(review.status === 'flagged' || review.status === 'pending') && (
          <>
            <DropdownMenuItem
              onClick={() => onPublish(review.id)}
              className="text-green-700"
            >
              <Check className="w-4 h-4 mr-2"/>
              Approve & Publish
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                const reason = prompt('Reason for hiding this review?');
                if (reason) onHide(review.id, reason);
              }}
              className="text-red-600"
            >
              <ArrowDownCircle className="w-4 h-4 mr-2" />
              Hide Review
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDelete}
              className="text-destructive"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </>
        )}
        <div className="border-t my-1"></div>
        {onAddNote && (
          <DropdownMenuItem onClick={handleAddNote}>
            <MessageSquare className="w-4 h-4 mr-2" />
            Add Note
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ReviewActionsDropdown;
