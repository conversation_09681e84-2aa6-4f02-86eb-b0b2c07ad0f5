{"name": "faa-agent-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--max_old_space_size=8192 && next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "input-otp": "^1.4.2", "jose": "^6.0.12", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-phone-number-input": "^3.4.12", "sharp": "^0.34.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9", "eslint-config-next": "15.3.5", "jsdom": "^26.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}