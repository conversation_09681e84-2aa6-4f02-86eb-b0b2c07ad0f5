import { Button } from '@/components/ui/button'
import { AuthFormHeading } from '@/components/ui/typography/headings'
import { cn } from '@/lib/utils'
import { ArrowLeftIcon } from 'lucide-react'
import React, { PropsWithChildren } from 'react'


interface AuthCardWrapperProps extends PropsWithChildren {
    /**
     * Title of the Card
     */
    title: string

    /**
     * Class name for the title
     */
    titleClassName?: string

    /**
     * Description of the Card
     */
    description?: string

    /**
     * Callback function for the back button,
     * Also visible the back button when this callback is provided
     */
    backButtonCallback?: () => void
}

/**
 * Component used to wrap the basic Registration Form and Login Form
 */
function AuthCardWrapper({ 
    children, 
    title, 
    titleClassName, 
    description, 
    backButtonCallback 
}: AuthCardWrapperProps) {
    return (
        <div className='flex flex-col gap-7 w-full'>
            <div className="flex justify-center items-center relative">
                {backButtonCallback && (
                    <Button
                        variant="muted"
                        size="icon"
                        type='button'
                        onClick={backButtonCallback}
                        className='absolute left-0 text-foreground/90'
                    >
                        <ArrowLeftIcon />
                    </Button>
                )}
                <div className="flex flex-col gap-2">
                    <h1 className={cn(
                        'text-2xl font-bold text-center font-heading mx-5',
                        titleClassName
                    )}>
                        {title}
                    </h1>
                    {description && (
                        <p className='text-sm text-muted-foreground text-center'>
                            {description}
                        </p>
                    )}
                </div>
            </div>
            {children}
        </div>
    )
}

export default AuthCardWrapper