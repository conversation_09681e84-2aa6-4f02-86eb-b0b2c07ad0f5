import { Star } from 'lucide-react';

interface ReviewStarsProps {
  rating: number;
  showNumber?: boolean;
}

const ReviewStars = ({ rating, showNumber = true }: ReviewStarsProps) => {
  return (
    <div className="flex items-center gap-2">
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
      {showNumber && <span className="text-sm font-medium">{rating}/5</span>}
    </div>
  );
};

export default ReviewStars;
