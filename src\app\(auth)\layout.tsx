import { IMAGES_ROUTES } from '@/constants/images-routes'
import { StaticImage } from '@/components/image'
import Logo from '@/components/logo'
import { PropsWithChildren } from 'react'


function AuthLayout({ children }: PropsWithChildren) {
    return (
        <div className='relative w-full grid grid-rows-[var(--height-nav)_1fr] min-h-screen md:grid-rows-1 md:grid md:grid-cols-2'>
            <nav className='px-dashboard-layout h-nav flex items-center justify-start top-0 inset-x-0 md:justify-start md:absolute'>
                <Logo />
            </nav>

            {/* Form Side */}
            <div className={`order-2 md:order-1 bg-background-light`}>
                <div className='flex flex-col justify-center items-center h-full mx-auto w-full pt-10 pb-24 px-5 relative md:px-0 md:pb-24 md:pt-20 md:max-w-[300px] lg:max-w-[400px] xl:max-w-[500px]'>
                    {children}
                    <div className='flex flex-col gap-3 items-center justify-center absolute bottom-5'>
                        <span className='text-muted-foreground text-sm text-center'>Find Any Agent 2025, All Rights Reserved</span>
                    </div>
                </div>
            </div>

            {/* Image Side */}
            <div className={`relative overflow-hidden order-1 shadow-custom hidden md:block md:shadow-none md:order-2`}>
                <StaticImage
                    alt=''
                    src={IMAGES_ROUTES.authHeroImage}
                    className={"w-full object-cover md:h-full dark:grayscale-[60%] md:absolute md:inset-0"}
                    priority
                    placeholder='blur'
                />
            </div>
        </div>
    )
}

export default AuthLayout