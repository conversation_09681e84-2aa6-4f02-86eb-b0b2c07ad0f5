import React, { ComponentProps } from 'react'
import Image from 'next/image'

type StaticImageProps = ComponentProps<typeof Image>

/**
 * StaticImage component, used to display static images,
 * it is a wrapper around the Next.js Image component,
 * it is used to display static images not dynamic,
 * it is used to display images that are in the public folder,
 * @param props - The props to pass to the Image component
 * @example
 * <StaticImage 
 *  src={logo} 
 *  alt="logo" 
 *  width={100} 
 *  height={100} 
 *  className="w-10 h-10"
 * />
 */
function StaticImage({ src, alt, width, height, ...props }: StaticImageProps) {
    return (
        <Image 
            src={src} 
            alt={alt} 
            width={width} 
            height={height} 
            {...props} 
        />
    )
}

export default StaticImage