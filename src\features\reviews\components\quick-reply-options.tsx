import { Button } from '@/shared/components/ui/button';

interface QuickReplyOptionsProps {
  onSelectReply: (message: string) => void;
  reviewRating: number;
}

const QuickReplyOptions = ({ onSelectReply, reviewRating }: QuickReplyOptionsProps) => {
  const getQuickReplies = (rating: number) => {
    if (rating >= 4) {
      return [
        "Thank you for your positive feedback! We're delighted to hear about your experience.",
        "We appreciate your kind words and are glad we could meet your expectations.",
        "Thank you for choosing us! Your satisfaction is our priority."
      ];
    } else if (rating >= 3) {
      return [
        "Thank you for your feedback. We appreciate your input and will work to improve.",
        "We value your review and are committed to enhancing our services.",
        "Thank you for taking the time to share your experience with us."
      ];
    } else {
      return [
        "We sincerely apologize for not meeting your expectations. We'd like to make this right.",
        "Thank you for bringing this to our attention. We take all feedback seriously.",
        "We're sorry to hear about your experience. Please contact us so we can address your concerns."
      ];
    }
  };

  const quickReplies = getQuickReplies(reviewRating);

  return (
    <div className="space-y-2">
      <p className="text-sm font-medium text-gray-700">Quick Reply Options:</p>
      <div className="flex flex-wrap gap-2">
        {quickReplies.map((reply, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => onSelectReply(reply)}
            className="text-xs h-auto py-2 px-3 whitespace-normal text-left"
          >
            {reply}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default QuickReplyOptions;
