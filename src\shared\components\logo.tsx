import Link from 'next/link'
import React from 'react'
import { StaticImage } from '@/components/image'
import { logo } from '@/constants/static-images'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { cn } from '@/lib/utils'

/**
 * Props for the Logo component
 * @param className - The class name to apply to the logo
 */
type LogoProps = {
    className?: string
}

/**
 * Logo component
 * @param className - The class name to apply to the logo
 */
function Logo({ className }: LogoProps) {
    return (
        <Link href={PAGE_ROUTES.home}>
            <StaticImage 
                src={logo} 
                alt="logo" 
                className={cn(
                    "w-18",
                    className
                )}
                priority
            />
        </Link>
    )
}

export default Logo