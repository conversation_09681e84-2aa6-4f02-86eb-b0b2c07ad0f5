"use client"

import { forgotPasswordFormSchema, ForgotPasswordFormSchema } from '@/auth/schemas/forms/login-form.schema'
import { Button } from '@/components/ui/button'
import { TextInput } from '@/form-inputs/text-input'
import { zodResolver } from '@hookform/resolvers/zod'
import React, { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { forgotPassword } from '@/auth/actions/auth.action'
import { handleApiResponse } from '@/lib/helpers/client.helpers'
import { useRouter } from 'next/navigation'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { errorToast } from '@/lib/toast'
import { setUserTempEmail } from '@/lib/helpers/local-storage.helpers'
import { API_ERROR_MESSAGES } from '@/constants/messages/api'
import { Mail } from 'lucide-react'

/**
 * This component is used to handle the forgot password form submission.
 * It is used to send an OTP to the user's email to reset their password.
 */
function ForgotPasswordForm() {
    const router = useRouter()
    
    // Initializing the forgot password form
    const form = useForm<ForgotPasswordFormSchema>({
        resolver: zodResolver(forgotPasswordFormSchema),
        defaultValues: {
            email: "",
        },
    })

    // Handle the form submission
    const onSubmit = async (data: ForgotPasswordFormSchema) => {
        const response = await forgotPassword(data)
        handleApiResponse({
            response,
            successMessage: "OTP sent successfully",
            hideDefaultErrorToast: true,
            onSuccess: () => {
                setUserTempEmail(data.email)
                router.push(PAGE_ROUTES.passwordOtpVerification)
            },
            onError: (error) => {
                if (error.message === API_ERROR_MESSAGES.otpAlreadySent) {
                    setUserTempEmail(data.email)
                    router.push(PAGE_ROUTES.passwordOtpVerification)
                } else {
                    errorToast(error.message)
                }
            },
        })
    }

    // Prefetch the password OTP verification route so it's ready by the time we navigate to it
    useEffect(() => {
        router.prefetch(PAGE_ROUTES.passwordOtpVerification)
    }, [router])

    return (
        <FormProvider {...form}>
            <form
                className='flex flex-col gap-6'
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <TextInput
                    name="email"
                    control={form.control}
                    placeholder='Enter your email'
                    LeftIcon={Mail}
                />
                <Button
                    type="submit"
                    isLoading={form.formState.isSubmitting}
                >
                    {form.formState.isSubmitting ? "Sending OTP" : "Reset Password"}
                </Button>
            </form>
        </FormProvider>
    )
}

export default ForgotPasswordForm