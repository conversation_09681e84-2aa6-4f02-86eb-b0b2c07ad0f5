/**
 * @fileoverview Typography Component - A flexible, variant-based typography system
 * 
 * This component provides a comprehensive typography system built on top of class-variance-authority (cva)
 * and Radix UI's Slot component. It offers semantic HTML elements with consistent styling variants.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import * as React from "react"

/**
 * Typography variants configuration using class-variance-authority
 * 
 * Defines all available styling variants for the Typography component including:
 * - Semantic variants (h1-h6, p, span, etc.)
 * - Weight variants (normal, medium, semibold, bold)
 * - Tone variants (default, muted, destructive, primary)
 * 
 * @remarks
 * Most developers only need the `variant` prop; other variants are optional
 * mix-ins for size overrides, color overrides, etc.
 */
const typographyVariants = cva([], {
    variants: {
        /**
         * The semantic and visual intent bundle
         * 
         * Each variant maps to appropriate HTML semantics with predefined styling:
         * - Headings (h1-h6): Different hierarchy levels with appropriate font sizes
         * - Body text (p, span, small): Common text elements with readable styling
         * - Inline elements (code): Styled inline code blocks
         * - Block elements (blockquote): Styled quote blocks
         */
        variant: {
            // Heading variants - semantic hierarchy with responsive sizing
            /** Primary heading - largest, most prominent */
            h1: "font-heading scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
            /** Secondary heading - section titles */
            h2: "font-heading scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0",
            /** Tertiary heading - subsection titles */
            h3: "font-heading scroll-m-20 text-2xl font-semibold tracking-tight",
            /** Quaternary heading - minor section titles */
            h4: "font-heading scroll-m-20 text-xl font-semibold tracking-tight",
            /** Quinary heading - smallest prominent heading */
            h5: "font-heading scroll-m-20 text-lg font-semibold tracking-tight",
            /** Smallest heading - inline section titles */
            h6: "font-heading scroll-m-20 text-base font-semibold tracking-tight",
            
            // Body text variants - readable content styling
            /** Standard paragraph text with optimal line height */
            p: "leading-7 [&:not(:first-child)]:mt-6",
            /** Inline text with normal line height */
            span: "leading-normal",
            /** Small text for captions, labels, etc. */
            small: "text-sm font-medium leading-none",
            /** Large introductory text */
            lead: "text-xl text-muted-foreground",
            /** Subdued text for secondary information */
            muted: "text-sm text-muted-foreground",
            
            // Inline formatting variants
            /** Inline code with background and monospace font */
            code: "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm",
            
            // Block formatting variants
            /** Block quote with left border and italic styling */
            blockquote: "mt-6 border-l-2 pl-6 italic",
        },
        
        
        /**
         * Color tone variants
         * 
         * Provides semantic color options using CSS custom properties
         */
        tone: {
            /** Default foreground color */
            default: "text-foreground",
            /** Muted/secondary text color */
            muted: "text-muted-foreground",
            /** Destructive/error text color */
            destructive: "text-destructive",
            /** Primary brand color */
            primary: "text-primary",
        },
    },
    
    /**
     * Default variant values
     * 
     * @remarks
     * No default `variant` is set to ensure compile-time safety - users must choose.
     * Other variants default to undefined (no additional classes applied).
     */
    defaultVariants: {
        tone: "default",
        variant: "p",
    },
})

/**
 * Props interface for the Typography component
 * 
 * Extends standard HTML element attributes and includes variant props from cva
 */
export interface TypographyProps
    extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
    /**
     * Render as a different HTML element
     * 
     * Override the default semantic HTML tag for this variant.
     * Useful for accessibility edge-cases where you need different semantics
     * but want to maintain the visual styling.
     * 
     * @example
     * ```tsx
     * <Typography variant="h1" as="h2">Visually h1, semantically h2</Typography>
     * ```
     */
    as?: React.ElementType
    
    /**
     * Adopt parent's semantics while keeping these styles
     * 
     * When true, the component will merge its styles with a child component
     * instead of rendering its own HTML element. This follows the same pattern
     * as other shadcn/ui components.
     * 
     * @example
     * ```tsx
     * <Typography variant="h1" asChild>
     *   <CustomHeading>Styled with Typography classes</CustomHeading>
     * </Typography>
     * ```
     */
    asChild?: boolean
}

/**
 * Mapping of typography variants to their default semantic HTML tags
 * 
 * This ensures that each variant uses appropriate HTML semantics by default,
 * while still allowing override via the `as` prop.
 */
const defaultTag: Record<
    NonNullable<TypographyProps["variant"]>,
    keyof React.JSX.IntrinsicElements
> = {
    h1: "h1",
    h2: "h2", 
    h3: "h3",
    h4: "h4",
    h5: "h5",
    h6: "h6",
    p: "p",
    span: "span",
    small: "small",
    lead: "p",     // Lead text uses paragraph semantics
    muted: "p",    // Muted text uses paragraph semantics
    code: "code",
    blockquote: "blockquote",
}

/**
 * Typography Component
 * 
 * A flexible typography component that provides consistent styling across your application.
 * Built with semantic HTML in mind and offers extensive customization through variants.
 * 
 * @param variant - The semantic and visual type of typography (required)
 * @param weight - Font weight override (optional)
 * @param tone - Color tone variant (optional, defaults to "default")
 * @param as - Override the default HTML element (optional)
 * @param asChild - Merge with child component instead of rendering own element (optional)
 * @param className - Additional Tailwind or CSS classes (optional)
 * @param children - Content to render
 * @param ref - Forwarded ref to the underlying HTML element
 * 
 * @returns A styled typography element with the specified variant and options
 * 
 * @example
 * Basic usage:
 * ```tsx
 * <Typography variant="h1">Main Heading</Typography>
 * <Typography variant="p">Body paragraph text</Typography>
 * <Typography variant="small" tone="muted">Caption text</Typography>
 * ```
 * 
 * @example
 * With custom styling:
 * ```tsx
 * <Typography 
 *   variant="h2" 
 *   tone="primary"
 *   className="mb-4"
 * >
 *   Custom Heading
 * </Typography>
 * ```
 * 
 * @example
 * Using asChild pattern:
 * ```tsx
 * <Typography variant="h1" asChild>
 *   <Link href="/home">Styled Link</Link>
 * </Typography>
 * ```
 * 
 * @example
 * Semantic override:
 * ```tsx
 * <Typography variant="h1" as="h2">
 *   Looks like h1, semantically h2
 * </Typography>
 * ```
 */
export const Typography = React.forwardRef<HTMLElement, TypographyProps>(
    (
        {
            variant,
            tone,
            as: Tag,
            asChild = false,
            className,
            ...props
        },
        ref,
    ) => {
        // Use Slot for asChild pattern, otherwise use custom tag or default semantic tag
        const Comp = asChild ? Slot : Tag ?? defaultTag[variant!]

        return (
            <Comp
                ref={ref as React.Ref<HTMLElement>}
                className={cn(
                    typographyVariants({ variant, tone }),
                    className,
                )}
                {...props}
            />
        )
    },
)

Typography.displayName = "Typography"
