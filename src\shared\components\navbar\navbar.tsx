import React from 'react'
import NotificationIcon from './notification-icon'
import { SidebarTrigger } from '@/ui/sidebar'
import { Alert } from '@/ui/alert/generic-alert'
import AvatarDropdownMenu from './avatar-dropdown-menu'
import VerifyNowButton from './verify-now-button'

function Navbar() {
    return (
        <nav className={`h-nav border-b px-dashboard-layout flex items-center justify-between gap-5 xl:justify-end`}>
            <SidebarTrigger className='bg-background size-10 text-2xl flex xl:hidden' />
            <div className="flex gap-4 items-center">
                <Alert
                    variant="soft"
                    type="warning"
                    title="Verification is required to list properties, projects, and access premium features and build trust"
                    className='hidden md:grid'
                />
                <VerifyNowButton />
                <NotificationIcon />
                <AvatarDropdownMenu />
            </div>
        </nav>
    )
}

export default Navbar