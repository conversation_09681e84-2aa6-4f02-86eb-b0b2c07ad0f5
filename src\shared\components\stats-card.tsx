import React from 'react'
import SurfaceCard from './surface-card'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface StatsCardProps {
    title: string
    value: string
    Icon: LucideIcon
    small?: string
    smallClassName?: string
    titleClassName?: string
    valueClassName?: string
    iconClassName?: string
    className?: string
}

function StatsCard({ title, value, Icon, small, smallClassName, titleClassName, valueClassName, iconClassName, className }: StatsCardProps) {
    return (
        <SurfaceCard className={cn('flex gap-3 flex-row justify-between items-start px-3 md:px-5', className)}>
            <div className='flex flex-col gap-1'>
                <div className='flex items-center gap-1.5'>
                    <h3 className={cn('text-sm font-medium text-foreground md:text-base', titleClassName)}>{title}</h3>
                </div>
                <div className="flex flex-col gap-1">
                    <p className={cn('text-xl font-bold md:text-2xl', valueClassName)}>{value}</p>
                    {small && <p className={cn('text-sm text-muted-foreground', smallClassName)}>{small}</p>}
                </div>
            </div>
            <Icon className={cn('size-5 mt-0.5 md:size-6', iconClassName)} />
        </SurfaceCard>
    )
}

export default StatsCard