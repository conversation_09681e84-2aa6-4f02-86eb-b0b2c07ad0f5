import React, { useEffect } from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { SELECT_OPTION_IDS } from '@/constants/select-option-ids'
import { TextInput } from '@/shared/components/form-inputs/text-input'
import { Button } from '@/shared/components/ui/button'
import { TrashIcon } from 'lucide-react'
import { parseIndustryOption } from '@/features/profile/helpers/options.helpers'
import { Step2FormType } from '@/features/profile/schemas/profile-registration/individual.schema'


function OtherPrimaryIndustry() {
    const {
        watch,
        control,
        trigger,
    } = useFormContext<Step2FormType>()

    const primaryIndustries = watch('primaryIndustries')
    
    const isOtherOptionSelected = primaryIndustries
        .map(industry => parseIndustryOption(industry))
        .some(industry => industry.id === SELECT_OPTION_IDS.OTHER_PRIMARY_INDUSTRY)
    
    const otherInputsLimit = 3 - primaryIndustries.length

    const {
        fields,
        append,
        remove,
    } = useFieldArray({
        name: "otherPrimaryIndustry",
    })

    // Add other input to the array
    useEffect(() => {
        // If the other option is selected and there are no other inputs
        // add an empty input field
        if (isOtherOptionSelected && fields.length === 0) {
            append("")
        }
        // If the other option is not selected, unregister the other inputs,
        // so that the other inputs are not submitted
        if (!isOtherOptionSelected) {
            fields.map((_, index) => {
                remove(index)
            })
        }
    }, [isOtherOptionSelected, fields])


    // Add other input to the array
    const addOtherInputHandler = async () => {
        const isValid = await trigger("otherPrimaryIndustry", { shouldFocus: true })
        if (isValid) {
            append("")
        }
    }

    // If not other option is selected, don't render the component
    if (!isOtherOptionSelected) return null

    return (
        <div className='flex flex-col gap-2'>
            <div className="flex flex-col gap-4">
                {fields
                    .map((field, index) => (
                        <TextInput
                            key={field.id}
                            name={`otherPrimaryIndustry.${index}`}
                            control={control}
                            label={`Other Primary Industry ${index > 0 ? `(${index + 1})` : ''}`}
                            className='h-10'
                            RightIcon={fields.length > 1 ? TrashIcon : undefined}
                            rightIconProps={{
                                className: "size-4 hover:text-destructive",
                                onClick: () => remove(index),
                            }}
                        />
                    ))
                }
            </div>
            {fields.length <= otherInputsLimit && (
                <Button
                    size={"sm"}
                    variant={"muted"}
                    type='button'
                    className='self-end '
                    onClick={addOtherInputHandler}
                >
                    Add more
                </Button>
            )}
        </div>
    )
}

export default OtherPrimaryIndustry