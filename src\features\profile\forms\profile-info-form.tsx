"use client"

import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Form<PERSON>rovider, useForm } from 'react-hook-form'
import { ProfileInfoFormSchema } from '@/profile/types/profile-form-schema.type'
import { zodResolver } from '@hookform/resolvers/zod'
import { profileInfoFormSchema } from '@/profile/lib/schemas/profile-forms'
import { TextInput } from '@/components/form-inputs/text-input'
import { SelectInput } from '@/components/form-inputs/select-input'
import { GENDER_OPTIONS } from '@/constants/select-options'
import SaveChangesButton from '@/profile/components/save-changes-button'
import { useProfileDetails } from '@/profile/context/profile-details-context'
import ProfileImageUploadInput from '@/shared/components/form-inputs/file-upload-input/profile-image-upload-input'



function ProfileInfoForm() {
    const { profileDetails } = useProfileDetails()

    const form = useForm<ProfileInfoFormSchema>({
        resolver: zodResolver(profileInfoF<PERSON><PERSON>chema),
        defaultValues: {
            firstName: profileDetails.user.firstName,
            middleName: profileDetails.user.middleName || undefined,
            lastName: profileDetails.user.lastName,
            gender: profileDetails.agentDetails.gender as (typeof GENDER_OPTIONS)[number]['value'],
            nationality: profileDetails.agentDetails.nationality || undefined,
            profilePhoto: profileDetails.user.profileImage ? [profileDetails.user.profileImage] : [],
        },
    })

    const onSubmit = (data: ProfileInfoFormSchema) => {
    }

    return (
        <FormProvider {...form}>
            <form 
                onSubmit={form.handleSubmit(onSubmit)} 
                noValidate
                className='flex flex-col gap-5'
            >
                <div className='grid grid-cols-1 gap-5 items-start lg:grid-cols-3'>
                    <div className='lg:col-span-3 flex justify-start'>
                        <ProfileImageUploadInput 
                            control={form.control}
                            name='profilePhoto'
                            // inputClassName='size-24'
                        />
                    </div>
                    <TextInput 
                        control={form.control}
                        name='firstName'
                        label='First Name'
                        placeholder='Enter your first name'
                        disabled
                    />
                    <TextInput
                        control={form.control}
                        name='middleName'
                        label='Middle Name'
                        placeholder='Enter your middle name'
                        disabled
                    />
                    <TextInput 
                        control={form.control}
                        name='lastName'
                        label='Last Name'
                        placeholder='Enter your last name'
                        disabled
                    />
                </div>
                <div className="grid gap-5 grid-cols-1 items-start lg:grid-cols-2">
                    <TextInput 
                        control={form.control}
                        name='nationality'
                        label='Nationality'
                        placeholder='Enter your nationality'
                        disabled
                    />
                    <SelectInput 
                        name='gender'
                        label='Gender'
                        placeholder='Select your gender'
                        control={form.control}
                        options={GENDER_OPTIONS}
                        disabled
                    />
                </div>
                <SaveChangesButton className='self-start' />
            </form>
        </FormProvider>
    )
}

export default ProfileInfoForm