import { LucideIcon } from 'lucide-react'
import React, { PropsWithChildren } from 'react'

export interface SectionHeadingProps extends PropsWithChildren {
    LeftIcon?: LucideIcon
}

function SectionHeading({ children, LeftIcon }: SectionHeadingProps) {
    return (
        <h2 className='text-xl font-semibold grid grid-cols-[auto_1fr] items-start gap-1.5 lg:text-xl'>
            {LeftIcon && <LeftIcon className='size-5 mt-1' />}
            {children}
        </h2>
    )
}

export default SectionHeading