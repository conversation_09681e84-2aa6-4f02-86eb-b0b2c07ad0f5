"use client"

import { Progress } from '@/components/ui/progress'
import { useFormSteps } from '@/shared/context/form-steps-context'
import React from 'react'

function ProfileFormProgressBar() {
    const { currentStep, totalSteps, progressPercentage } = useFormSteps()

    return (
        <div className="w-full space-y-1 bg-white">
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-start text-sm bg-white pl-2">
                <span className='text-muted-foreground'>Step {currentStep} / {totalSteps}</span>
            </div>
            
        </div>
    )
}

export default ProfileFormProgressBar 