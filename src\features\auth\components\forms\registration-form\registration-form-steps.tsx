"use client"

import { useRegistrationFormSteps } from '@/features/auth/context/registration-form-steps-context'
import React from 'react'
import StepOneForm from './step-one-form'
import StepTwoForm from './step-two-form'

function RegistrationFormSteps() {
    const { currentStep } = useRegistrationFormSteps()

    if (currentStep === 1) {
        return <StepOneForm />
        
    } else if (currentStep === 2) {
        return <StepTwoForm />
    }

    return (
        <div>
            <h1>Invalid step</h1>
        </div>
    )
}

export default RegistrationFormSteps