import { z } from "zod"

export function makeOptionalField<T extends z.ZodTypeAny>(
    field: T,
    fieldType?: "text" | "number" | "boolean" | "date" | "array" | "object"
) {
    if (fieldType === "text") {
        return field.optional().or(z.literal('').transform(() => undefined))

    } else if (fieldType === "number") {
        return field.optional().or(z.literal('').transform(() => undefined))

    } else if (fieldType === "boolean") {
        return field.optional().or(z.literal('').transform(() => undefined))

    } else if (fieldType === "date") {
        return field.optional().or(z.literal('').transform(() => undefined))

    } else if (fieldType === "array") {
        return field.optional().or(z.literal([]).transform(() => undefined))
        
    } else {
        return field.optional().or(z.literal('').transform(() => undefined))
    }
}