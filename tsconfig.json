{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"],
      "@/public/*": ["./public/*"],
      "@/types/*": ["./src/shared/types/*"],
      "@/constants/*": ["./src/shared/constants/*"],
      "@/lib/*": ["./src/shared/lib/*"],
      "@/hooks/*": ["./src/shared/hooks/*"],
      "@/components/*": ["./src/shared/components/*"],
      "@/context/*": ["./src/shared/context/*"],
      "@/ui/*": ["./src/shared/components/ui/*"],
      "@/form-inputs/*": ["./src/shared/components/form-inputs/*"],
      "@/features/*": ["./src/features/*"],
      "@/auth/*": ["./src/features/auth/*"],
      "@/styles/*": ["./src/app/styles/*"],
      "@/schemas/*": ["./src/shared/schemas/*"],
      "@/helpers/*": ["./src/shared/lib/helpers/*"],
      "@/profile/*": ["./src/features/profile/*"],
      "@/properties/*": ["./src/features/properties/*"],
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
