"use client"

import React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { FormProvider, useForm } from 'react-hook-form'
import { ContactInfoFormSchema } from '@/profile/types/profile-form-schema.type'
import { contactInfoFormSchema } from '@/profile/lib/schemas/profile-forms'
import { TextInput } from '@/components/form-inputs/text-input'
import { PhoneInput } from '@/components/form-inputs/phone-input'
import SaveChangesButton from '@/profile/components/save-changes-button'
import { useProfileDetails } from '@/profile/context/profile-details-context'


function ContactInfoForm() {
    const { profileDetails } = useProfileDetails()

    const form = useForm<ContactInfoFormSchema>({
        resolver: zodResolver(contactInfoFormSchema),
        defaultValues: {
            email: profileDetails.user.email,
            contact: profileDetails.user.phone,
            whatsapp: profileDetails.user.whatsappContact || undefined,
            location: profileDetails.user.location || undefined,
            address: profileDetails.user.address || undefined,
        },
    })

    const onSubmit = (data: ContactInfoFormSchema) => {
    }

    return (
        <FormProvider {...form}>
            <form 
                onSubmit={form.handleSubmit(onSubmit)} 
                noValidate
                className='grid grid-cols-1 gap-5 items-start md:grid-cols-2'
            >
                <TextInput
                    control={form.control}
                    name='email'
                    label='Email'
                    placeholder='Enter your email'
                    disabled
                    inputGroupClassName='md:col-span-2'
                />
                {/* <PhoneInput
                    control={form.control}
                    name='contact'
                    label='Contact'
                    placeholder='Enter your contact'
                />
                <PhoneInput
                    control={form.control}
                    name='whatsapp'
                    label='Whatsapp'
                    placeholder='Enter your whatsapp'
                /> */}
                <TextInput
                    control={form.control}
                    name='location'
                    label='Location'
                    placeholder='Enter your location'
                />
                <TextInput
                    control={form.control}
                    name='address'
                    label='Address'
                    placeholder='Enter your address'
                />
                <SaveChangesButton className='justify-self-start md:col-span-2' />
            </form>
        </FormProvider>
    )
}

export default ContactInfoForm