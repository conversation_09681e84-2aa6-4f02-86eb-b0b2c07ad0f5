import { FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import React from 'react'
import FormGroup from '../form-group'
import { MultiSelect, MultiSelectProps } from '@/components/ui/multi-select'
import { useForm<PERSON>ield } from '@/components/ui/form'

/**
 * Props interface for the MultiSelectInputWithGroup component.
 * 
 * This interface combines FormGroup styling/layout props with MultiSelect functionality,
 * creating a complete form input solution that includes label, description, error handling,
 * and the multi-select dropdown itself.
 */
export interface MultiSelectInputWithGroupProps extends
    FormGroupPropsWithoutChildrenAndIcons {
    /** Configuration props for the underlying MultiSelect component */
    multiSelectProps: MultiSelectProps,
}

/**
 * MultiSelectInputWithGroup Component
 * 
 * A lower-level component that combines a MultiSelect dropdown with FormGroup wrapper.
 * This component provides the visual structure and accessibility features for a complete
 * form input, including label, description, error states, and the multi-select functionality.
 * 
 * This component is typically used internally by higher-level form components like
 * MultiSelectInput, but can also be used directly when you need more control over
 * form state management.
 * 
 * Features:
 * - Integrates MultiSelect with FormGroup for consistent styling
 * - Automatic error state handling via useFormField hook
 * - Accessibility support with aria-invalid attributes
 * - Flexible prop forwarding to both FormGroup and MultiSelect
 * 
 * @param props - The component props combining FormGroup and MultiSelect configuration
 * @returns A complete form input with multi-select dropdown
 * 
 * @example
 * ```tsx
 * <MultiSelectInputWithGroup
 *   label="Skills"
 *   description="Select your technical skills"
 *   multiSelectProps={{
 *     options: skillOptions,
 *     placeholder: "Choose skills...",
 *     onValueChange: handleChange
 *   }}
 * />
 * ```
 */
function NewMultiSelectInputWithGroup({
    multiSelectProps,
    ...formGroupProps // Extract FormGroup props
}: MultiSelectInputWithGroupProps) {
    // Get form field state from context to handle error states
    const { error } = useFormField()
    
    return (
        <FormGroup {...formGroupProps}>
            <MultiSelect 
                {...multiSelectProps}
                // Set aria-invalid for accessibility when field has validation errors
                aria-invalid={!!error}
            />
        </FormGroup>
    )
}

export default NewMultiSelectInputWithGroup