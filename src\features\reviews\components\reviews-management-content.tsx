'use client';

import { useEffect, useState } from 'react';
import { TReview } from '../types/review.type';
import { getReviewsForManagement } from '../actions/reviews.action';
import { useReviewsHandlers } from '../lib/use-reviews-handlers';
import ReviewsStatsGrid from './reviews-stats-grid';
import ReviewsFilters from './reviews-filters';
import ReviewsCardsList from './reviews-cards-list';

const ReviewsManagementContent = () => {
  const [reviews, setReviews] = useState<TReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await getReviewsForManagement();
        
        if (response.success && response.data) {
          setReviews(response.data);
        } else {
          setError(response.message || 'Failed to fetch reviews');
          setReviews([]);
        }
      } catch (error) {
        console.error('Failed to fetch reviews:', error);
        setError('An unexpected error occurred while fetching reviews');
        setReviews([]);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, []);

  const {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    selectedReview,
    setSelectedReview,
    moderationResponse,
    setModerationResponse,
    filteredReviews,
    stats,
    handleAddNote,
    handleDeleteReview,
    handleStatusChange,
    handleFlag,
    handleHide,
    handleRestore,
    handlePublish,
    handleModerationResponse,
    handleDropdownViewDetails,
    handleDropdownAddReview
  } = useReviewsHandlers(reviews);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="flex flex-col items-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="text-gray-600">Loading reviews...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-red-800 mb-2">Error Loading Reviews</h3>
            <p className="text-red-600">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
  <div className="space-y-6">
    <ReviewsStatsGrid stats={stats} />

    <div className="bg-white rounded-lg shadow">
      {/* Filters Section */}
      <div className="p-2 border-b">
        <ReviewsFilters 
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          selectedFilter={statusFilter}
          onFilterChange={setStatusFilter}
        />
      </div>

      {/* Reviews List Section */}
      <div className="p-4">
        <ReviewsCardsList
          reviews={reviews}
          filteredReviews={filteredReviews}
          onStatusChange={handleStatusChange}
          onFlag={handleFlag}
          onRestore={handleRestore}
          onHide={handleHide}
          onPublish={handlePublish}
          onViewDetails={handleDropdownViewDetails}
          onAddReview={handleDropdownAddReview}
          onAddNote={handleAddNote}
          onReply={(id) => console.log('Reply to review:', id)}
        />
      </div>
    </div>
  </div>
);

};

export default ReviewsManagementContent;
