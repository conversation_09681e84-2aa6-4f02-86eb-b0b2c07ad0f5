"use client"


import { Step6FormType, step6Schema } from '@/profile/schemas/profile-registration/individual.schema'
import BaseForm from '@/components/base-form'
import { useFormSteps } from '@/context/form-steps-context'
import useIndividualRegistrationFormStore from '@/context/individual-registration-form-store'
import { CheckboxInput } from '@/components/form-inputs/checkbox-input'
import { CONFIRMATION_OPTIONS } from '@/constants/select-options'
import NavigationButton from '../navigation-button'
import { verifyIndividualProfile } from '@/features/profile/actions/verify-profile'

function StepSixForm() {
    const { form: mainForm, updateStep } = useIndividualRegistrationFormStore()
    const { goToNextStep} = useFormSteps()

    const onSubmit = async (data: Step6FormType) => {
        const result = await verifyIndividualProfile(mainForm)
        updateStep('step6', data)
        goToNextStep()
    }
    return (
        <BaseForm
            schema={step6Schema}
            onSubmit={onSubmit}
            defaultValues={{
                confirmation: mainForm.step6.confirmation || [],
            }}
        >
            {({ control }) => (
                <div className="flex flex-col gap-5 pt-2 mb-10 w-full">
                    <CheckboxInput
                        control={control}
                        name="confirmation"
                        options={CONFIRMATION_OPTIONS}
                        optionLabelClassName='font-semibold text-base'
                        inputGroupClassName='py-5 w-full'
                        optionsContainerClassName='w-full mx-auto flex'
                    />
                    <NavigationButton />
                </div>
            )}
        </BaseForm>
    )
}

export default StepSixForm 