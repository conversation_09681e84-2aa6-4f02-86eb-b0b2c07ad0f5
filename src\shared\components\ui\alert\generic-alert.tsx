import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { CircleFadingArrowUpIcon, OctagonAlert, ShieldAlert } from "lucide-react"

// Variants follow the pattern shown in success-alert.tsx lines 23-51
// Base layout styles keep the same grid layout like the shadcn alert
const genericAlertVariants = cva(
  "relative w-full rounded-md border px-4 py-1 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-2 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",
  {
    variants: {
      /* Visual variants (overall visual language) */
      variant: {
        soft: "border-transparent border-0",
        bootstrap: "border",
        outline: "border bg-transparent text-inherit dark:text-inherit",
      },
      /* Semantic status type */
      type: {
        success:
          "bg-emerald-500/10 dark:bg-emerald-600/30 border-emerald-300 dark:border-emerald-600/70 [&>svg]:text-emerald-600 dark:[&>svg]:text-emerald-300",
        warning:
          "bg-amber-500/10 dark:bg-amber-600/30 bg-amber-500/10 dark:bg-amber-600/30 border-amber-300 dark:border-amber-600/70 [&>svg]:text-amber-500",
        error:
          "bg-destructive/10 dark:bg-destructive/20 border-destructive/50 dark:border-destructive/70 [&>svg]:text-destructive dark:[&>svg]:text-destructive/70",
        info:
          "bg-blue-500/10 dark:bg-blue-600/30 border-blue-300 dark:border-blue-600/70 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-300",
      },
      /* Size variants */
      size: {
        sm: "px-3 py-1.5 text-xs [&>svg]:size-3",
        default: "px-4 py-2 text-sm",
        lg: "px-5 py-3 text-base [&>svg]:size-5",
      },
    },
    defaultVariants: {
      variant: "soft",
      type: "info",
      size: "default",
    },
  }
)

interface GenericAlertProps
  extends React.ComponentProps<"div">,
    VariantProps<typeof genericAlertVariants> {}

const GenericAlert = React.forwardRef<HTMLDivElement, GenericAlertProps>(
  ({ className, variant, type, size, ...props }, ref) => {
    return (
      <div
        ref={ref}
        role="alert"
        data-slot="alert"
        className={cn(genericAlertVariants({ variant, type, size }), className)}
        {...props}
      />
    )
  }
)
GenericAlert.displayName = "GenericAlert"

type GenericAlertTitleProps = React.ComponentProps<"div">;
function GenericAlertTitle({ className, ...props }: GenericAlertTitleProps) {
  return (
    <div
      data-slot="alert-title"
      className={cn(
        "col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",
        className
      )}
      {...props}
    />
  )
}

type GenericAlertDescriptionProps = React.ComponentProps<"div">;
function GenericAlertDescription({
  className,
  ...props
}: GenericAlertDescriptionProps) {
  return (
    <div
      data-slot="alert-description"
      className={cn(
        "text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",
        className
      )}
      {...props}
    />
  )
}

type AlertProps = GenericAlertProps & {
    title: string
    description?: string
}

type AlertType = VariantProps<typeof genericAlertVariants>["type"];

const renderIcon = (type: AlertType) => {
    const className = "h-4 w-4"
    switch (type) {
        case "success":
            return <CircleFadingArrowUpIcon className={className} />
        case "warning":
            return <ShieldAlert className={className} />
        case "error":
            return <OctagonAlert className={className} />
        case "info":
            return <CircleFadingArrowUpIcon className={className} />
        default:
            return <CircleFadingArrowUpIcon className={className} />
    }
}

function Alert( {
    title,
    description,
    className,
    variant,
    type,
    size,
    ...props
}: AlertProps) {
    return (
        <GenericAlert 
            className={cn(genericAlertVariants({ variant, type, size }), className)}
            {...props}
        >
            {renderIcon(type)}
            <GenericAlertTitle>{title}</GenericAlertTitle>
            {description && <GenericAlertDescription>{description}</GenericAlertDescription>}
        </GenericAlert>
    )
}
export {
  Alert,
  GenericAlert,
  GenericAlertTitle,
  GenericAlertDescription,
  genericAlertVariants,
  type GenericAlertTitleProps,
  type GenericAlertDescriptionProps,
  type GenericAlertProps,
} 