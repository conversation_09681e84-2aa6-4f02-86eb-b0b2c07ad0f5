"use client"

import React from 'react'
import { Form<PERSON>rovider, useForm, useFormContext } from 'react-hook-form'
import { useCompleteProfileFormOptions } from '@/profile/context/complete-profile-form-options-context'
import { MultiSelectInput } from '@/form-inputs/multiple-select-input'
import { getIndustryRolesOptions } from '@/features/profile/actions/profile-roles.actions'
import { ApiIndustryRoleOption } from '@/features/profile/types/profile-roles.type'
import { parseIndustryOption, stringifyIndustryOption } from '@/features/profile/helpers/options.helpers'
import { SELECT_OPTION_IDS } from '@/constants/select-option-ids'
import {  Step2FormType, step2Schema } from '@/features/profile/schemas/profile-registration/individual.schema'
import useIndividualRegistrationFormStore from '@/shared/context/individual-registration-form-store'
import { useFormSteps } from '@/shared/context/form-steps-context'
import { zodResolver } from '@hookform/resolvers/zod'
import NewOtherPrimaryIndustry from '../other-primary-industry'
import NavigationButton from '../navigation-button'


function StepTwoForm() {
    const { updateStep, form: store } = useIndividualRegistrationFormStore()
    const { setRoles } = useCompleteProfileFormOptions()
    const { goToNextStep } = useFormSteps()

    const form = useForm<Step2FormType>({
        resolver: zodResolver(step2Schema),
        defaultValues: {
            primaryIndustries: store.step2.primaryIndustries || [],
            otherPrimaryIndustry: store.step2.otherPrimaryIndustry || [],
        },
    })

    const { isDirty } = form.formState

    const getRole = async (primaryIndustries: string[]) => {
        const parsedIndustries = primaryIndustries
            .map(industry => parseIndustryOption(industry))

        // Fetch the roles for the selected industries, by calling the API for each industry
        const promiseList = parsedIndustries
            .filter(industry => industry.id !== SELECT_OPTION_IDS.OTHER_PRIMARY_INDUSTRY)
            .map(industry => getIndustryRolesOptions(Number(industry.id)))

        // Wait for all the API calls to complete
        const responses = await Promise.all(promiseList)

        // Flatten the responses to get the roles
        const roles = responses
            .filter(response => response.success)
            .reduce((acc, response) => {
                acc[response.data[0].parentId?.toString() || ""] = response.data
                return acc
            }, {} as Record<string, ApiIndustryRoleOption[]>)

        // Update the roles in the context
        setRoles(roles)
    }


    const onSubmit = async (data: Step2FormType) => {
        if (isDirty) {
            await getRole(data.primaryIndustries)
        }
        updateStep('step2', data)
        goToNextStep()
    }

    return (
        <FormProvider {...form}>
            <form 
                className='flex flex-col gap-5'
                onSubmit={form.handleSubmit(onSubmit)}
                noValidate
            >
                <SelectIndustryInput />
                <NewOtherPrimaryIndustry />
                <NavigationButton />
            </form>
        </FormProvider>
    )
}


function SelectIndustryInput() {
    const { control, watch } = useFormContext<Step2FormType>()
    const { industries } = useCompleteProfileFormOptions()

    const otherIndustries = watch('otherPrimaryIndustry')

    // Calculating the max options selected for the multi-select input
    const otherIndustriesLength = otherIndustries?.length || 0
    const maxValue = otherIndustriesLength > 0 ? 4 : 3
    const maxOptionsSelected = maxValue - otherIndustriesLength

    return (
        <MultiSelectInput
            name={`primaryIndustries`}
            label="Industry"
            control={control}
            placeholder='Select industries'
            maxOptionsSelected={maxOptionsSelected}
            options={industries.map(industry => ({
                label: industry.name,
                value: stringifyIndustryOption(industry),
            }))}
            hideSelectAllOption
        />
    )
}


export default StepTwoForm 