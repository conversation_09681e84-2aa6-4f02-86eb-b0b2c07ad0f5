'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface RegistrationFormStepsContextType {
    currentStep: number;
    totalSteps: number;
    goToNextStep: () => void;
    goToPreviousStep: () => void;
    goToStep: (step: number) => void;
    hasNextStep: boolean;
    hasPreviousStep: boolean;
    isFirstStep: boolean;
    isLastStep: boolean;
}

const RegistrationFormStepsContext = createContext<RegistrationFormStepsContextType | undefined>(undefined);

interface RegistrationFormStepsProviderProps {
    children: ReactNode;
    totalSteps?: number;
    initialStep?: number;
}

export function RegistrationFormStepsProvider({
    children,
    totalSteps = 2,
    initialStep = 1,
}: RegistrationFormStepsProviderProps) {
    const [currentStep, setCurrentStep] = useState(initialStep);

    const hasNextStep = currentStep < totalSteps;
    const hasPreviousStep = currentStep > 1;
    const isFirstStep = currentStep === 1;
    const isLastStep = currentStep === totalSteps;

    const goToNextStep = () => {
        if (hasNextStep) {
            setCurrentStep(currentStep + 1);
        }
    }

    const goToPreviousStep = () => {
        if (hasPreviousStep) {
            setCurrentStep(current => current - 1);
        }
    }

    const goToStep = (step: number) => {
        if (step >= 1 && step <= totalSteps) {
            setCurrentStep(step);
        }
    }

    const value: RegistrationFormStepsContextType = {
        currentStep,
        totalSteps,
        goToNextStep,
        goToPreviousStep,
        goToStep,
        hasNextStep,
        hasPreviousStep,
        isFirstStep,
        isLastStep,
    };

    return (
        <RegistrationFormStepsContext.Provider value={value}>
            {children}
        </RegistrationFormStepsContext.Provider>
    );
};

export const useRegistrationFormSteps = (): RegistrationFormStepsContextType => {
    const context = useContext(RegistrationFormStepsContext);

    if (context === undefined) {
        throw new Error('useRegistrationFormSteps must be used within a RegistrationFormStepsProvider');
    }

    return context;
};
