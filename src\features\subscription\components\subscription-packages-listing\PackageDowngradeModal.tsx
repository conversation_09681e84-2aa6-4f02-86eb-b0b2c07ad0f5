'use client';

import React from 'react';
import { SubscriptionPackage } from './data';
import Modal from '@/shared/components/modals/modal';

interface PackageDowngradeModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    current: SubscriptionPackage;
    target: SubscriptionPackage;
}

const PackageDowngradeModal: React.FC<PackageDowngradeModalProps> = ({ isOpen, onClose, onConfirm, current, target }) => {
    const allFeatures = Array.from(new Set([...current.features.map((f) => f.featureConstant), ...target.features.map((f) => f.featureConstant)]));

    const getFeatureValue = (pkg: SubscriptionPackage, constant: string) => pkg.features.find((f) => f.featureConstant === constant)?.featureValue ?? '—';

    const renderComparisonRows = () =>
        allFeatures.map((constant) => {
            const currentVal = getFeatureValue(current, constant);
            const targetVal = getFeatureValue(target, constant);
            const isDifferent = currentVal !== targetVal;

            return (
                <tr key={constant} className={isDifferent ? 'bg-red-50 text-red-600' : ''}>
                    <td className="border border-gray-200 px-3 py-2 font-medium capitalize">{constant.replace(/_/g, ' ')}</td>
                    <td className="border border-gray-200 px-3 py-2">{String(currentVal)}</td>
                    <td className="border border-gray-200 px-3 py-2">{String(targetVal)}</td>
                </tr>
            );
        });

    return (
        <Modal isOpen={isOpen} onClose={onClose} classes="max-w-5xl w-full">
            <div className="mb-4">
                <h2 className="text-xl font-semibold text-gray-800">Review Changes Before Downgrading</h2>
                <p className="mt-1 text-lg text-gray-600">
                    You are about to switch from the <strong className="text-gray-800">{current.name}</strong> plan
                    <span className="text-gray-500"> ({current.interval.toUpperCase()})</span> to the <strong className="text-gray-800">{target.name}</strong> plan
                    <span className="text-gray-500"> ({target.interval.toUpperCase()})</span>.
                    <br />
                    Please review the differences below before confirming your downgrade.
                </p>
            </div>

            <div className="mb-6 max-h-[50vh] overflow-auto rounded-md border">
                <table className="w-full border-collapse text-sm">
                    <thead>
                        <tr className="bg-gray-100 text-left">
                            <th className="border border-gray-200 px-3 py-2">Feature</th>
                            <th className="border border-gray-200 px-3 py-2">Current Plan</th>
                            <th className="border border-gray-200 px-3 py-2">Downgrade Plan</th>
                        </tr>
                    </thead>
                    <tbody>{renderComparisonRows()}</tbody>
                </table>
            </div>

            <div className="flex justify-end gap-4">
                <button onClick={onClose} className="rounded border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100">
                    Cancel
                </button>
                <button
                    onClick={() => {
                        onConfirm();
                        onClose();
                    }}
                    className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                >
                    Confirm Downgrade
                </button>
            </div>
        </Modal>
    );
};

export default PackageDowngradeModal;
