import { IndividualProfileSchema } from '@/features/profile/schemas/profile-registration/individual.schema'
import { Button } from '@/shared/components/ui/button'
import { useFormSteps } from '@/shared/context/form-steps-context'
import React from 'react'
import { useFormContext } from 'react-hook-form'

function NavigationButton() {
    const {
        hasNextStep,
        isLastStep,
    } = useFormSteps()
    const { formState: { isSubmitting, isValid} } = useFormContext()
    return (
        <Button 
            className='w-full mt-3' 
            type='submit'
            isLoading={isSubmitting}
            disabled={isLastStep && !isValid}
            size={"lg"}
        >
            {isLastStep ? 'Complete' : 'Next'}
        </Button>
    )
}

export default NavigationButton