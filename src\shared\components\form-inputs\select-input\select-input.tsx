import React from 'react'
import { FormGroupPropsWithoutChildren, FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import { SelectOptions } from '@/shared/types'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { FormField } from '@/ui/form'
import BaseSelectInput from './base-select-input'
import FormGroup from '../form-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectTriggerProps, SelectValue } from '../../ui/select'
import { cn } from '@/lib/utils'
import { BaseFormFieldProps } from '@/shared/types/inputs'

export interface SelectInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends FormGroupPropsWithoutChildren, BaseFormFieldProps<TFieldValues, TName>, Omit<SelectTriggerProps, keyof BaseFormFieldProps<TFieldValues, TName>> {
    options: SelectOptions,
    placeholder?: string,
}

function SelectInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
    options,
    control,
    name,
    placeholder,
    className,
    label,
    labelClassName,
    errorMessageClassName,
    inputGroupClassName,
    disabled,
    ...props
}: SelectInputProps<TFieldValues, TName>) {
    return (
        <FormField
            control={control}
            name={name}
            render={({ field, fieldState }) => (
                <FormGroup
                    label={label}
                    labelClassName={labelClassName}
                    errorMessageClassName={errorMessageClassName}
                    inputGroupClassName={inputGroupClassName}
                >
                    <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value || ""}
                        name={name}
                        disabled={disabled}
                    >
                        <SelectTrigger
                            aria-invalid={!!fieldState.error}
                            {...field}
                            {...props}
                            className={className}
                            ref={field.ref}
                        >
                            <SelectValue
                                placeholder={placeholder}
                            />
                        </SelectTrigger>
                        <SelectContent>
                            {options.map((option) => (
                                <SelectItem
                                    key={option.value}
                                    value={option.value}
                                    className='cursor-pointer'
                                >
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </FormGroup>
            )}
        />
    )
}




export default SelectInput