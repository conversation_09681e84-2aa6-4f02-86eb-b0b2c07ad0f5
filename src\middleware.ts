import { NextResponse, NextRequest } from 'next/server'
import { verifyCookie } from './features/auth/actions/session.actions'

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
    // Verify cookie
    const session = await verifyCookie()

    // If <PERSON><PERSON> not found or invalid, redirect to login
    if(!session) {
        return NextResponse.redirect(new URL('/login', request.url))
    }

    // If user is logged in and tries to access "/" redirect to dashboard
    if(request.nextUrl.pathname === '/') {
        return NextResponse.redirect(new URL('/dashboard', request.url))
    }

    return NextResponse.next()
}

/**
 * Middleware configuration.
 * This is the list of paths that the middleware will run on.
 */
export const config = {
    matcher: [
        '/dashboard',
        '/profile',
        "/",
    ],
}