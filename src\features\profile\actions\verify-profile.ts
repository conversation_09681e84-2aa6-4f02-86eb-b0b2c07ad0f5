"use server"

import { executeApiRequest } from "@/lib/helpers/actions.helpers"
import { PostBody, REQUEST_TYPE } from "@/types/action-helper.type"
import API_ENDPOINTS from "@/constants/api-routes"
import { IndividualProfileSchema } from "../schemas/profile-registration/individual.schema"
import { parseIndustryAndRole } from "../helpers/options.helpers"

export async function verifyIndividualProfile(data: IndividualProfileSchema) {
    const formData = new FormData()
    // Step 1
    formData.append('first_name', data.step1.firstName);
    formData.append('middle_name', data.step1.middleName || "");
    formData.append('last_name', data.step1.lastName);
    formData.append('nationality', data.step1.nationality);
    formData.append('gender', data.step1.gender);
    formData.append('phone_number', data.step1.phoneNumber);

    // Step 2
    formData.append('primaryIndustry', data.step2.primaryIndustries.join(","));
    formData.append('primaryIndustryOther', data.step2.otherPrimaryIndustry.join(","));

    // Step 3
    Object.entries(data.step3.defaultIndustryRoles).forEach(([key, value]) => {
        if(value !== "other") {
            formData.append('agentType[]', value);
        } 
    });

    const otherRoles = Object.values(data.step3.defaultIndustryOtherRoles || {})
    if(otherRoles.length > 0) {
        formData.append('agentTypeOther', otherRoles.join(','));
    }

    // Step 4
    Object.entries(data.step4.defaultIndustryRoles).forEach(([key, value], index) => {
        const {role} = parseIndustryAndRole(key)
        const indexPrefix = `form8[${index}]`;
        formData.append(`${indexPrefix}[id]`, key || '');
        formData.append(`${indexPrefix}[roletype]`, role.id.toString());
        formData.append(`${indexPrefix}[hasLicense]`, value.isLicensed ? "yes" : "no");
        if(value.isLicensed && value.isLicensed === "yes") {
            formData.append(`${indexPrefix}[licenseNumber]`, value.licenseNumber);
            formData.append(`${indexPrefix}[licenseAuthority]`, "");
            formData.append(`${indexPrefix}[licenseAuthorityOther]`, "");
            formData.append(`${indexPrefix}[licenseexpiryDate]`, value.expirationDate.toISOString());
            if(value.licenseFile.length > 0) {
                value.licenseFile.forEach((file) => {
                    formData.append(`${indexPrefix}[licenseFile][]`, file);
                });
            }
        }
    });

    // if (Array.isArray(tempData.form8)) {
    //     for (let i = 0; i < tempData.form8.length; i++) {
    //         const item: any = tempData.form8[i];
    //         
    

    //         forData.append(`${indexPrefix}[id]`, item.id || '');
    //         forData.append(`${indexPrefix}[roletype]`, item.roletype || '');
    //         forData.append(`${indexPrefix}[hasLicense]`, item.hasLicense || '');

    //         // Only append license-related fields if hasLicense is "yes"
    //         if (item.hasLicense === 'yes') {
    //             forData.append(`${indexPrefix}[licenseNumber]`, item.licenseNumber || '');
    //             forData.append(`${indexPrefix}[licenseAuthority]`, item.licenseAuthority || '');
    //             forData.append(`${indexPrefix}[licenseAuthorityOther]`, item.licenseAuthorityOther || '');
    //             forData.append(`${indexPrefix}[licenseexpiryDate]`, item.licenseexpiryDate || '');

    //             // Upload each licenseFile
    //             if (Array.isArray(item.licenseFile)) {
    //                 for (let j = 0; j < item.licenseFile.length; j++) {
    //                     forData.append(`${indexPrefix}[licenseFile][]`, item.licenseFile[j]);
    //                 }
    //             }
    //         }
    //     }
    // }


    // Todo: Need to understand about this
    const response = await executeApiRequest({
        method: REQUEST_TYPE.put,
        url: API_ENDPOINTS.COMPLETE_PROFILE,
        body: formData as unknown as PostBody,
        withAuth: true,
        options: {
            headers: {
                "Content-Type": "multipart/form-data",
            }
        }
    })
    return response
}
