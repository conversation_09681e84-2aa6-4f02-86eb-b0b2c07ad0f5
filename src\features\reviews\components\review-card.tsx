import { Reply, Flag, MessageSquare } from 'lucide-react';
import { But<PERSON> } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Textarea } from '@/shared/components/ui/textarea';
import { TReview } from '../types/review.type';
import ReviewStars from './review-stars';
import QuickReplyOptions from './quick-reply-options';
import { useState } from 'react';

interface ReviewCardProps {
  review: TReview;
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  onViewDetails: (id: string) => void;
  onAddReview: () => void;
  onAddNote?: (id: string, note: string) => void;
  onReply?: (id: string) => void;
}

const ReviewCard = ({
  review,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  onViewDetails,
  onAddReview,
  onAddNote,
  onReply,
}: ReviewCardProps) => {
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyText, setReplyText] = useState('');

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'published':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'flagged':
        return 'destructive';
      case 'hidden':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const handleQuickReply = (message: string) => {
    setReplyText(message);
  };

  const handleReply = (id: string) => {
    if (onReply) {
      onReply(id);
    }
    // Reset reply state after sending
    setReplyingTo(null);
    setReplyText('');
  };

  const handleFlag = (id: string) => {
    const reason = prompt('Please provide a reason for flagging this review:');
    if (reason) {
      onFlag(id, reason);
    }
  };

  // Check if review has existing replies
  const hasReplies = review.comments && review.comments.some(comment => comment.type === 'note');
  const replyComment = hasReplies ? review.comments?.find(comment => comment.type === 'note') : null;

  return (
    
    <div className={`border-l-4 rounded-md bg-white shadow-sm ${review.status === 'flagged' ? 'border-l-red-500' : 'border-l-blue-500'} mb-4`}>
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold">{review.reviewerName}</h3>
              <div className="flex">
                <ReviewStars rating={review.rating} showNumber={false} />
              </div>
              <Badge variant={getStatusBadgeVariant(review.status)}>
                {review.status}
              </Badge>
              {review.status === 'flagged' && (
                <Badge variant="destructive" className="ml-2">
                  <Flag className="h-3 w-3 mr-1" />
                  Flagged
                </Badge>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-1">{review.targetName}</p>
            <p className="text-sm text-gray-500">{formatDate(review.createdAt)}</p>
          </div>
          <div className="flex gap-2">
            {!hasReplies && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setReplyingTo(review.id)}
              >
                <Reply className="h-4 w-4 mr-1" />
                Reply
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleFlag(review.id)}
            >
              <Flag className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <p className="text-gray-700 mb-4">{review.comment}</p>

        {hasReplies && replyComment && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-semibold text-blue-600">Your Reply:</span>
            </div>
            <p className="text-sm text-gray-700">{replyComment.comment}</p>
          </div>
        )}

        {replyingTo === review.id && (
          <div className="mt-4 space-y-3">
            <QuickReplyOptions
              onSelectReply={handleQuickReply}
              reviewRating={review.rating}
            />
            <Textarea
              placeholder="Write your reply or select from quick options above..."
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              rows={4}
            />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => handleReply(review.id)}
                disabled={!replyText.trim()}
              >
                Send Reply
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setReplyingTo(null);
                  setReplyText("");
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
    
  );
};

export default ReviewCard;
