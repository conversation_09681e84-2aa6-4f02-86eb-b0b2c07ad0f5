import React, { ComponentProps } from 'react'
import {
    DateInput as BaseDateInput,
} from '@/shared/components/ui/date-input'
import { Control, ControllerRenderProps } from 'react-hook-form'
import { FieldPath } from 'react-hook-form'
import { FieldValues } from 'react-hook-form'
import { FormField, useFormField } from '../ui/form'
import { FormGroupPropsWithoutChildrenAndIcons } from '@/shared/types/inputs/form-group.type'
import FormGroup from './form-group'
import { Calendar } from '../ui/calendar'

export interface DateInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends FormGroupPropsWithoutChildrenAndIcons {
    name: TName,
    control: Control<TFieldValues>,
    disabledDate?: ComponentProps<typeof Calendar>["disabled"]
    fromYear?: number
    toYear?: number
}

function DateInput<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({ 
    control, 
    name,
    disabledDate,
    fromYear,
    toYear,
    ...formGroupProps
}: DateInputProps<TFieldValues, TName>) {
    return (
        <FormField 
            control={control}
            name={name}
            render={({ field }) => (
                <FormGroup {...formGroupProps}>
                    <WrappedDateInput 
                        field={{ ...field }} 
                        calendarProps={{
                            disabled: disabledDate,
                            fromYear,
                            toYear
                        }} 
                    />
                </FormGroup>
            )}
        />
    )
}

const WrappedDateInput = (props: {
    field: ControllerRenderProps<FieldValues, string>, 
    calendarProps?: ComponentProps<typeof Calendar>
}) => {
    const { error } = useFormField()
    const { field, calendarProps } = props
    const { disabled, fromYear, toYear, ...otherCalendarProps } = calendarProps || {}
    
    return (
        <BaseDateInput 
            date={field.value}
            setDate={field.onChange}
            ref={field.ref}
            ariaInvalid={!!error}
            disabled={disabled}
            fromYear={fromYear}
            toYear={toYear}
            {...otherCalendarProps}
        />
    )
}

export default DateInput