"use client"

import React, { ComponentProps } from 'react'
import { FormControl, FormField, FormItem } from '@/ui/form'
import { Control, FieldPath, FieldValues } from 'react-hook-form'
import { cn } from '@/lib/utils'
import * as RadixRadioGroup from '@radix-ui/react-radio-group'
import { Label } from '@/ui/label'
import { SelectOption } from '@/shared/types'

/**
 * Props for the FormRadioInput component
 * 
 * @template TFieldValues - The type of the form values object
 * @template TName - The type of the field name (must be a valid path in TFieldValues)
 */
type CardCheckboxInputProps<
    TFieldValues extends FieldValues = FieldValues,
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> = {
    /** The field name in the form (must be a valid path in TFieldValues) */
    name: TName,
    /** React Hook Form control object */
    control: Control<TFieldValues>,

    options: SelectOption[],

    radioButtonOptions?: ComponentProps<typeof RadixRadioGroup.Root>,
}

function CardCheckboxInput<
    TFieldValues extends FieldValues = FieldValues, 
    TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({ control, name, options, radioButtonOptions }: CardCheckboxInputProps<TFieldValues, TName>) {
    return (
        <FormField 
            control={control}
            name={name}
            render={({ field }) => (
                <FormItem>
                    <FormControl>
                        <RadixRadioGroup.Root
                            value={field.value}
                            onValueChange={field.onChange}
                            className="w-full flex flex-col gap-4"
                            {...radioButtonOptions}
                        >
                            {options.map((option) => (
                                <RadixRadioGroup.Item
                                    key={`${name}-${option.value}`}
                                    value={option.value}
                                    className={cn(
                                        "relative group border border-input rounded-md py-4 px-4 text-start grid grid-cols-[1fr] items-start justify-center gap-3 cursor-pointer hover:bg-background focus-visible:ring-3 focus-visible:ring-ring transition-all duration-200",
                                        "data-[state=checked]:bg-primary/10 data-[state=checked]:border-primary",
                                    )}
                                >
                                    {/* <span className='size-5 mt-1 aspect-square border rounded-full group-data-[state=checked]:bg-primary'></span> */}
                                    <Label className="text-sm leading-relaxed cursor-pointer text-foreground/70 group-data-[state=checked]:text-primary text-center w-full justify-center">{option.label}</Label>
                                </RadixRadioGroup.Item>
                            ))}
                        </RadixRadioGroup.Root>
                    </FormControl>
                </FormItem>
            )}
        />
    )
}

export default CardCheckboxInput