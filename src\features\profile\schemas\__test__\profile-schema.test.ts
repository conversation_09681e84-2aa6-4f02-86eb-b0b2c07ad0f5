import { describe, expect, test } from "vitest";
import { form1Schema, Form1Schema } from "../profile.schema";



// // Testing dateSchema
// // describe("Testing dateSchema", () => {
// //     test("should validate the date schema", () => {
// //         const date = "2025-03-10T01:12:30.000Z"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })

// //     test("validating 2025-03-13 date", () => {
// //         const date = "2025-03-13"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })

// //     test("validating 2025/03/13 date", () => {
// //         const date = "2025/03/13"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })
// // })


// // Testing phoneSchema
// describe("Testing phoneSchema", () => {
//     test("should validate the phone schema", () => {
//         const phone = "+11234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })

//     test("should validate the phone schema", () => {
//         const phone = "+11234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })

//     test("should validate the phone schema", () => {
//         const phone = "1234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })
//     test("should validate the phone schema 8978978997", () => {
//         const phone = "8978978997"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })
// })

// Testing selectSchema
// describe("Testing Form 1", () => {
//     const form1: Form1Schema = {
//         firstName: "John",
//         lastName: "Doe",
//         middleName: "Doe",
//         phoneNumber: "+923001234567",
//         nationality: "Pakistan",
//         gender: "male",
//         location: "Lahore",
//     } 

//     test("should validate the form 1 schema", () => {   
//         const parsed = form1Schema.safeParse(form1)
//         if(!parsed.success) {
//             console.log(parsed.error.flatten().fieldErrors);
//         }
//         expect(parsed.success).toBe(true)
//     })
// })

