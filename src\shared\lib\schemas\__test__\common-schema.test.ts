import { describe, expect, test } from "vitest";
import { dateSchema, phoneSchema, selectSchema, textSchema, } from "../common.schema";
import { GENDER_OPTIONS } from "@/constants/select-options";
import { CLIENT_ERROR_MESSAGES, INPUT_ERROR_MESSAGES } from "@/constants/messages/client";
import z from "zod";


// // Testing dateSchema
// // describe("Testing dateSchema", () => {
// //     test("should validate the date schema", () => {
// //         const date = "2025-03-10T01:12:30.000Z"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })

// //     test("validating 2025-03-13 date", () => {
// //         const date = "2025-03-13"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })

// //     test("validating 2025/03/13 date", () => {
// //         const date = "2025/03/13"
// //         const parsed = dateSchema.safeParse(date)
// //         expect(parsed.success).toBe(true)
// //     })
// // })


// // Testing phoneSchema
// describe("Testing phoneSchema", () => {
//     test("should validate the phone schema", () => {
//         const phone = "+11234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })

//     test("should validate the phone schema", () => {
//         const phone = "+11234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })

//     test("should validate the phone schema", () => {
//         const phone = "1234567890"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })
//     test("should validate the phone schema 8978978997", () => {
//         const phone = "8978978997"
//         const parsed = phoneSchema.safeParse(phone)
//         expect(parsed.success).toBe(true)
//     })
// })

// Testing selectSchema
// describe("Testing selectSchema", () => {
//     test("should return required error when value is undefined", () => {
//         const value = undefined
//         const parsed = selectSchema(GENDER_OPTIONS).safeParse(value)
//         if (parsed.success) {
//             throw new Error("Expected error")
//         }
//         const error = parsed.error
//         const flattened = z.flattenError(error);
//         const errorMessage = flattened.formErrors[0]
//         expect(errorMessage).toBe(INPUT_ERROR_MESSAGES.required)
//     })

//     test("should return invalid option error when value is not in the options", () => {
//         const value = "invalid"
//         const parsed = selectSchema(GENDER_OPTIONS).safeParse(value)
//         if(parsed.success) {
//             throw new Error("Expected error")
//         }
//         const error = parsed.error
//         const flattened = z.flattenError(error);
//         const errorMessage = flattened.formErrors[0]
//         expect(errorMessage).toBe(INPUT_ERROR_MESSAGES.invalidOption)
//     })

//     test("should return success when value is in the options", () => {
//         const value = "male"
//         const parsed = selectSchema(GENDER_OPTIONS).safeParse(value)
//         expect(parsed.success).toBe(true)
//     })
// })

describe("Testing textSchema", () => {
    test("should validate the text schema", () => {
        const text = "John Doe"
        const parsed = textSchema.safeParse(text)
        expect(parsed.success).toBe(true)
    })
})