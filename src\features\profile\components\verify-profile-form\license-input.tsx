import React from 'react'
import { YES_NO_OPTIONS } from '@/shared/constants/select-options'
import CheckboxCard from '../checkbox-card'
import RadioInput from '@/components/form-inputs/radio-input'
import { TextInput } from '@/components/form-inputs/text-input'
import DateInput from '@/shared/components/form-inputs/date-input'
import { FileUploadInput } from '@/shared/components/form-inputs/file-upload-input/file-upload-input'
import { INPUT_IMAGE_SIZE } from '@/shared/constants/input-image-size'
import {  FieldPath,  useFormContext } from 'react-hook-form'
import { Step4FormType } from '@/profile/schemas/profile-registration/individual.schema'

interface LicenseCheckboxCardProps {
    /**
     * Role options is type string,
     * But we formatted this "{roleId}-{roleName}-{industryId}"
     * So we need to split it to get the roleId
     */
    fieldName: FieldPath<Step4FormType>,

    industryName: string,
    roleName: string,
}

function NewLicenseCheckboxCard({ fieldName, industryName, roleName }: LicenseCheckboxCardProps) {
    const { control, watch } = useFormContext<Step4FormType>()
    const fieldValue = watch(fieldName)
    const isLicensedValue = fieldValue?.isLicensed === "yes"

    return (
        <CheckboxCard>
            <h3 className='text-lg font-semibold pb-0.5'>{roleName}<small className='text-sm text-muted-foreground'>{` (${industryName})`}</small></h3>
            <RadioInput
                control={control}
                label={`Do you hold any government-issued certifications/permit/licenses required for you to legally operate in the role you have chosen?`}
                labelClassName='text-sm text-muted-foreground'
                options={YES_NO_OPTIONS}
                name={`${fieldName}.isLicensed`}
            />
            {isLicensedValue && (
                <div className="flex flex-col gap-5 pt-5">
                    <TextInput
                        control={control}
                        name={`${fieldName}.licenseNumber`}
                        label="License Number"
                    />
                    <DateInput
                        control={control}
                        name={`${fieldName}.expirationDate`}
                        label="Expiration Date"
                        disabledDate={(date) => date < new Date()}
                        toYear={2030}
                    />
                    <FileUploadInput
                        control={control}
                        name={`${fieldName}.licenseFile`}
                        label="License File"
                        multiple
                        maxFiles={3}
                        maxSize={INPUT_IMAGE_SIZE.LICENSE_PHOTO}
                    />

                </div>
            )}
        </CheckboxCard>
    )
}

export default NewLicenseCheckboxCard