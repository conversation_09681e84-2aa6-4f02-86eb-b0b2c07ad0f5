"use client"

import { USER_TEMP_EMAIL } from '@/constants/local-storage-keys'
import React, { useEffect, useState } from 'react'

function OtpVerificationText() {
    const [email, setEmail] = useState('')

    useEffect(() => {
        const email = localStorage.getItem(USER_TEMP_EMAIL)
        if(email) {
            setEmail(email)
        }
    }, [])
    return (
        <p className='text-sm text-muted-foreground text-center' >
            Enter the 4-digit OTP we sent to your provided email address {email}
        </p>
    )
}

export default OtpVerificationText