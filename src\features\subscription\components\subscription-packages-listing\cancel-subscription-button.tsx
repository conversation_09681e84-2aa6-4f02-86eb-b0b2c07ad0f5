"use client";
import React from "react";

interface CancelSubscriptionButtonProps {
  onCancel: () => void;
}

const CancelSubscriptionButton = ({
  onCancel,
}: CancelSubscriptionButtonProps) => {
  return (
    <div className="flex justify-end mb-4 px-7">
      <button
        onClick={onCancel}
        className="text-red-500 border border-red-500 hover:bg-red-50 px-4 py-2 rounded-md text-sm font-inter transition-colors duration-200"
      >
        Cancel Current Subscription
      </button>
    </div>
  );
};

export default CancelSubscriptionButton;
