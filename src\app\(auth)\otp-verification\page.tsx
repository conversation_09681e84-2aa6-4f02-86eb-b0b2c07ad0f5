"use client"

import React from 'react'
import { resendOtp, verifyOtp } from '@/features/auth/actions/auth.action'
import { PAGE_ROUTES } from '@/constants/page-routes'
import OtpVerificationLayout from '@/components/otp-verification-layout'

function OtpVerificationPage() {
    return (
        <OtpVerificationLayout 
            header="Enter Confirmation Code"
            otpVerificationFormProps={{
                verifyOtpFn: verifyOtp,
                successRedirectUrl: PAGE_ROUTES.login,
                resendButtonProps: {
                    resendOtpFn: resendOtp,
                },
            }}
        />
    )
}

export default OtpVerificationPage