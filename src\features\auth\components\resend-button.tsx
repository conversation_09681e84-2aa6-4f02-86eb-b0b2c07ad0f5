"use client"

import React, { useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { resendOtp } from '../actions/auth.action'
import { handleApiResponse } from '@/lib/helpers/client.helpers'
import { USER_TEMP_EMAIL } from '@/constants/local-storage-keys'
import { Button } from '@/components/ui/button'
import { OtpVerificationFormSchema } from '@/auth/schemas/forms/registration-form.schema'
import { ApiResponse } from '@/shared/types/utility.type'
import { useRouter } from 'next/navigation'

/**
 * The timer for the resend OTP functionality
 */
export const OTP_TIMER_SECONDS = 60

export interface ResendButtonProps {
    /**
     * Server action to resend OTP
     */
    resendOtpFn: (email: string) => Promise<ApiResponse>,
}

/**
 * This component is used to handle the resend OTP functionality.
 * It also handles the timer for the resend OTP functionality.
 */
function ResendButton({ resendOtpFn }: ResendButtonProps) {
    const form = useFormContext<OtpVerificationFormSchema>()
    const [timer, setTimer] = useState(OTP_TIMER_SECONDS)
    const [isResendMode, setIsResendMode] = useState(false) 
    const [isLoading, setIsLoading] = useState(false)

    // Handles Otp resend functionality
    const handleResend = async () => {
        setIsLoading(true)
        const email = localStorage.getItem(USER_TEMP_EMAIL)
        if (email) {
            const res = await resendOtpFn(email)
            handleApiResponse({
                response: res,
                successMessage: "OTP resent successfully",
                onSuccess: () => {
                    setTimer(OTP_TIMER_SECONDS)
                    setIsResendMode(false)
                    form.reset({ otp: '', email: email || '' })
                },
            })
            setIsLoading(false)
        }
    }
    
    // This is used to handle the timer for the resend OTP functionality
    useEffect(() => {
        if (isResendMode) {
            form.reset({ otp: '', email: localStorage.getItem(USER_TEMP_EMAIL) || '' })
        }
        if (timer > 0 && !isResendMode) {
            const interval = setInterval(() => {
                setTimer(prev => {
                    if (prev <= 1) {
                        setIsResendMode(true)
                        return 0
                    }
                    return prev - 1
                })
            }, 1000)
            return () => clearInterval(interval)
        }
    }, [timer, isResendMode])

    // Checking if the user is not in resend mode, then show the timer
    if(!isResendMode) {
        return (
            <p className="text-sm text-primary/80 mt-2 text-center">
                Resend code in {timer}s
            </p>
        )
    }

    // If the user is in resend mode, then show the resend button
    return (
        <Button
            className='w-full mt-1 max-w-[400px] underline hover:bg-transparent text-primary hover:text-primary/80 underline-offset-2'
            type="button"
            onClick={handleResend}
            variant={"ghost"}
            size={"sm"}
            isLoading={isLoading}
        >
            {isLoading ? "Resending" : "Resend Code"}
        </Button>
    )
}

export default ResendButton