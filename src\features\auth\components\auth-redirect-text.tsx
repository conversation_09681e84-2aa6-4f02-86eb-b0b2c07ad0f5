import React from 'react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface AuthRedirectTextProps {
    /**
     * The prompt shown before the link (e.g. "Don't have an account?")
     */
    prompt: string
    /**
     * The clickable link text (e.g. "Register")
     */
    linkText: string
    /**
     * Destination href for the link
     */
    href: string
    /**
     * Optional additional class names for the outer wrapper
     */
    className?: string
}

/**
 * Displays a small text prompt used on auth pages to redirect users
 * between login & registration screens (or any similar pages).
 */
function AuthRedirectText({ prompt, linkText, href, className }: AuthRedirectTextProps) {
    return (
        <p className={cn(
            "text-sm text-muted-foreground text-center flex items-center gap-1 justify-center",
            className
        )}>
            {prompt}
            <Link
                href={href}
                className="text-primary hover:underline"
            >
                {linkText}
            </Link>
        </p>
    )
}

export default AuthRedirectText 