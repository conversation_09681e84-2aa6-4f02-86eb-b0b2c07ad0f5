"use client"

import React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form<PERSON>rov<PERSON>, useFieldArray, useForm, useFormContext } from 'react-hook-form'
import { ProfessionalInfoFormSchema } from '@/profile/types/profile-form-schema.type'
import { professionalInfoFormSchema } from '@/profile/lib/schemas/profile-forms'
import { TextInput } from '@/components/form-inputs/text-input'
import { Button } from '@/components/ui/button'
import { PlusIcon, TrashIcon } from 'lucide-react'
import { FormLabel } from '@/components/ui/form'
import SaveChangesButton from '@/profile/components/save-changes-button'
import { SelectInput } from '@/components/form-inputs/select-input'
import TextareaInput from '@/components/form-inputs/textarea-input'
import { useProfileDetails } from '@/profile/context/profile-details-context'
import { useProfileSelectOptions } from '@/profile/context/profile-select-options-context'



function ProfessionalInfoForm() {
    const { profileDetails } = useProfileDetails()
    const { industryOptions } = useProfileSelectOptions()

    const form = useForm<ProfessionalInfoFormSchema>({
        resolver: zodResolver(professionalInfoFormSchema),
        defaultValues: {
            workPlace: profileDetails.agentDetails.employerName || undefined,
            industry: profileDetails.agentDetails.industryMission || undefined,
            roleType: profileDetails.agentDetails.position || undefined,
            description: profileDetails.agentDetails.summary || undefined,
            languages: profileDetails.agentDetails.languages || undefined,
        },
    })

    const onSubmit = () => {

    }

    return (
        <FormProvider {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                noValidate
                className='grid grid-cols-1 gap-5 items-start md:grid-cols-2'
            >
                <TextInput
                    control={form.control}
                    name='workPlace'
                    label='Work Place'
                    placeholder='Enter your work place'
                    inputGroupClassName='md:col-span-2'
                    disabled
                />
                <SelectInput
                    control={form.control}
                    name='industry'
                    label='Industry'
                    placeholder='Enter your industry'
                    options={industryOptions.map(option => ({
                        label: option.name,
                        value: option.id.toString(),
                    }))}
                    disabled
                />
                <TextInput
                    control={form.control}
                    name='roleType'
                    label='Role Type'
                    placeholder='Enter your role type'
                    disabled
                />
                <TextareaInput
                    control={form.control}
                    name='description'
                    label='Description'
                    placeholder='Enter your description'
                    inputGroupClassName='md:col-span-2'
                    className='h-28'
                />
                <SpecializationField />
                <SaveChangesButton className='justify-self-start md:col-span-2' />
            </form>
        </FormProvider>
    )
}



export function SpecializationField() {
    const form = useFormContext<ProfessionalInfoFormSchema>()

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: 'specialization'
    })
    const handleAddSpecialization = () => {
        append({ value: '' })
    }

    return (
        <div className='flex flex-col gap-1.5 md:col-span-2'>
            <div className='flex flex-col gap-1.5'>
                <FormLabel>
                    Specialization
                </FormLabel>
                {fields.map((field, index) => (
                    <div key={field.id} className='flex gap-2'>
                        <TextInput
                            control={form.control}
                            name={`specialization.${index}.value`}
                            placeholder='Enter your specialization'
                            RightIcon={TrashIcon}
                            rightIconProps={{
                                className: "hover:text-destructive",
                                onClick: () => remove(index),
                            }}
                        />
                    </div>
                ))}
            </div>
            <Button
                variant={"outline"}
                className='self-start px-1'
                type='button'
                onClick={handleAddSpecialization}
                size={"sm"}
            >
                <PlusIcon />
                Add Specialization
            </Button>
        </div>
    )
}

export default ProfessionalInfoForm 