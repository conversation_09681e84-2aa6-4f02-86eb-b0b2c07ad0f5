import { INPUT_IMAGE_SIZE } from "@/shared/constants/input-image-size"
import { INPUT_ERROR_MESSAGES } from "@/shared/constants/messages/client"
import { dateSchema, fileSchemaArray } from "@/shared/lib/schemas/common.schema"
import { textSchema } from "@/shared/lib/schemas/common.schema"
import { z } from "zod"


export const licenseSchema = z.discriminatedUnion('isLicensed', [
    z.object({
        isLicensed: z.literal("no"),
    }),
    z.object({
        isLicensed: z.literal("yes"),
        licenseNumber: textSchema,
        expirationDate: dateSchema,
        licenseFile: fileSchemaArray(INPUT_IMAGE_SIZE.LICENSE_PHOTO),
    }),
    z.object({
        isLicensed: z.literal(undefined),
    })
]).refine((data) => {
    // If isLicensed is undefined, return false
    if (data.isLicensed === undefined) {
        return false
    }
    return true;
}, {
    error: INPUT_ERROR_MESSAGES.required,
    path: ["isLicensed"],
})

export const licenseRecordSchema = z.record(z.string(), licenseSchema)


export type LicenseSchema = z.infer<typeof licenseSchema>
export type LicenseRecordSchema = z.infer<typeof licenseRecordSchema>
