import PageHeading from '@/components/page-heading'
import SurfaceCard from '@/components/surface-card'
import { BriefcaseIcon, CameraIcon, PhoneIcon } from 'lucide-react'
import React from 'react'
import ProfileInfoForm from '@/profile/forms/profile-info-form'
import AccountOverviewInfo from '@/profile/components/account-overview-info'
import ContactInfoForm from '@/profile/forms/contact-info-form'
import ProfessionalInfoForm from '@/profile/forms/professional-info-form'
import { getProfileInfo } from '@/profile/actions'
import { getIndustriesOptions } from '@/profile/actions/profile-roles.actions'
import { GENDER_OPTIONS } from '@/shared/constants/select-options'
import { ProfileDetailsContextProvider } from '@/features/profile/context/profile-details-context'
import { ProfileSelectOptionsContextProvider } from '@/features/profile/context/profile-select-options-context'
import { sleep } from '@/shared/lib/utils'

async function ProfilePage() {
    const res = await getProfileInfo()
    const industryOptions = await getIndustriesOptions()

    if (!res.success || !industryOptions.success) {
        throw new Error(res.message)
    }

    const profileDetails = res.data

    return (
        <ProfileDetailsContextProvider profileDetails={profileDetails}>
            <ProfileSelectOptionsContextProvider industryOptions={industryOptions.data}>
                <div className='flex flex-col gap-5'>
                    <PageHeading
                        title='Profile Settings'
                        description='Manage your account information and preferences'
                    />
                    <div className='grid grid-cols-1 gap-5 xl:grid-cols-[70%_1fr] lg:gap-5'>
                        <div className="flex flex-col gap-5">
                            <SurfaceCard
                                heading='Profile Information & Basic Information'
                                headingProps={{
                                    LeftIcon: CameraIcon,
                                }}
                            >
                                <ProfileInfoForm />
                            </SurfaceCard>
                            <SurfaceCard
                                heading='Contact Information'
                                headingProps={{ LeftIcon: PhoneIcon }}
                            >
                                <ContactInfoForm />
                            </SurfaceCard>
                            <SurfaceCard
                                heading='Professional Information'
                                headingProps={{
                                    LeftIcon: BriefcaseIcon,
                                }}
                            >
                                <ProfessionalInfoForm />
                            </SurfaceCard>
                        </div>
                        <div className=''>
                            <AccountOverviewInfo />
                        </div>
                    </div>
                </div>
            </ProfileSelectOptionsContextProvider>
        </ProfileDetailsContextProvider>
    )
}

export default ProfilePage