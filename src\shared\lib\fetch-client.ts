import { AUTH_COOKIE_NAME } from "@/constants/cookie-names";
import { cookies } from "next/headers";


export type FetchClientProps = {
    url: string,
    options?: RequestInit,
    withAuth?: boolean,
}

/**
 * Function handle un-authorized user.
 * If user session is expired or some other auth reason, user is redirected to login page.
 * @param props @FetchClientWithAuthProps
 * @returns Api response
 */
export async function fetchClient({ url, options, withAuth = false }: FetchClientProps) {
    let sessionCookie = null
    if(withAuth) {
        sessionCookie = (await cookies()).get(AUTH_COOKIE_NAME);
    }
    // Create headers object
    const headers: Record<string, string> = {
        ...(options?.headers as Record<string, string>),
        ...(withAuth ? { "Cookie": `${AUTH_COOKIE_NAME}=${sessionCookie?.value}` } : {}),
    };

    const apiResponse = await fetch(url, {
        ...options,
        headers,
    })

    return apiResponse
}