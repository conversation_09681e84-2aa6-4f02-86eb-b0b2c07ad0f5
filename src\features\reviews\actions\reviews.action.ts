'use server';

import API_ENDPOINTS from "@/shared/constants/api-routes";
import { executeApiRequest } from "@/shared/lib/helpers/actions.helpers";
import { REQUEST_TYPE } from "@/shared/types/action-helper.type";
import { ApiResponse } from "@/shared/types/utility.type";
import { TReview, TReviewLegacy, ReviewStats, ApiReviewsResponse } from '../types/review.type';
import { mapApiReviewsToFrontend } from '../lib/review-mapper';

/**
 * Get reviews for management page.
 * This action fetches the agent's reviews from the API.
 */
'use server';

import API_ENDPOINTS from "@/shared/constants/api-routes";
import { executeApiRequest } from "@/shared/lib/helpers/actions.helpers";
import { REQUEST_TYPE } from "@/shared/types/action-helper.type";
import { ApiResponse } from "@/shared/types/utility.type";
import { TReview, TReviewLegacy, ReviewStats, ApiReviewsResponse } from '../types/review.type';
import { mapApiReviewsToFrontend } from '../lib/review-mapper';
import { fetchReviewsAPI } from '../lib/custom-fetch';

/**
 * Get reviews for management page.
 * This action fetches the agent's reviews from the API.
 */
export async function getReviewsForManagement(): Promise<ApiResponse<TReview[]>> {
  try {
    console.log('Fetching reviews from API endpoint:', API_ENDPOINTS.GET_MY_REVIEWS);
    
    // Use custom fetch function that handles the specific auth requirements
    const response = await fetchReviewsAPI<ApiReviewsResponse>({
      url: API_ENDPOINTS.GET_MY_REVIEWS,
      method: 'GET',
    });
    
    console.log('API Response:', response);
    
    if (!response.success) {
      console.error('API returned error:', response.message);
      return {
        success: false,
        message: response.message || 'Failed to fetch reviews',
        status: response.status || 500,
      };
    }

    if (!response.data || !response.data.reviews) {
      console.error('Invalid API response structure:', response.data);
      return {
        success: false,
        message: 'Invalid response structure from API',
        status: 500,
      };
    }

    // Map API response to frontend format
    const mappedReviews = mapApiReviewsToFrontend(response.data.reviews);
    console.log('Mapped reviews:', mappedReviews);
    
    return {
      success: true,
      data: mappedReviews,
      message: 'Reviews fetched successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return {
      success: false,
      message: 'An unexpected error occurred while fetching reviews',
      status: 500,
    };
  }
}

/**
 * Get review statistics.
 * This calculates statistics from the reviews data.
 */
export const getReviewStats = async (): Promise<ApiResponse<ReviewStats>> => {
  try {
    // Get reviews data first
    const reviewsResponse = await getReviewsForManagement();
    
    if (!reviewsResponse.success || !reviewsResponse.data) {
      return {
        success: false,
        message: 'Failed to calculate stats',
        status: 500,
      };
    }

    const reviews = reviewsResponse.data;
    const stats: ReviewStats = {
      total: reviews.length,
      published: reviews.filter(r => r.status === 'published').length,
      pending: reviews.filter(r => r.status === 'pending').length,
      flagged: reviews.filter(r => r.status === 'flagged').length,
      hidden: reviews.filter(r => r.status === 'hidden').length,
      averageRating: reviews.length > 0 
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
        : 0,
      replied: 0, // TODO: Calculate when reply data is available
    };

    return {
      success: true,
      data: stats,
      message: 'Stats calculated successfully',
      status: 200,
    };
  } catch (error) {
    console.error('Error calculating review stats:', error);
    return {
      success: false,
      message: 'An error occurred while calculating stats',
      status: 500,
    };
  }
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use getReviewsForManagement instead
 */
export const getReviews = async (): Promise<{ data: TReviewLegacy[] }> => {
  // TODO: Replace with actual API call or remove if not needed
  const reviews: TReviewLegacy[] = [];
  return { data: reviews };
};

// Action functions for review management
export const updateReviewStatus = async (
  reviewId: string, 
  status: TReview['status']
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Updating review ${reviewId} status to ${status}`);
    
    return {
      success: true,
      message: `Review status updated to ${status}`
    };
  } catch (error) {
    console.error('Error updating review status:', error);
    return {
      success: false,
      message: 'Failed to update review status'
    };
  }
};

export const flagReview = async (
  reviewId: string, 
  reason: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Flagging review ${reviewId} with reason: ${reason}`);
    
    return {
      success: true,
      message: 'Review has been flagged for moderation'
    };
  } catch (error) {
    console.error('Error flagging review:', error);
    return {
      success: false,
      message: 'Failed to flag review'
    };
  }
};

export const hideReview = async (
  reviewId: string, 
  reason: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Hiding review ${reviewId} with reason: ${reason}`);
    
    return {
      success: true,
      message: 'Review has been hidden'
    };
  } catch (error) {
    console.error('Error hiding review:', error);
    return {
      success: false,
      message: 'Failed to hide review'
    };
  }
};

export const publishReview = async (
  reviewId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Publishing review ${reviewId}`);
    
    return {
      success: true,
      message: 'Review has been published'
    };
  } catch (error) {
    console.error('Error publishing review:', error);
    return {
      success: false,
      message: 'Failed to publish review'
    };
  }
};

export const deleteReview = async (
  reviewId: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Deleting review ${reviewId}`);
    
    return {
      success: true,
      message: 'Review has been deleted'
    };
  } catch (error) {
    console.error('Error deleting review:', error);
    return {
      success: false,
      message: 'Failed to delete review'
    };
  }
};

export const addNoteToReview = async (
  reviewId: string, 
  note: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // TODO: Implement with actual API endpoint when available
    console.log(`Adding note to review ${reviewId}: ${note}`);
    
    return {
      success: true,
      message: 'Note has been added to the review'
    };
  } catch (error) {
    console.error('Error adding note to review:', error);
    return {
      success: false,
      message: 'Failed to add note to review'
    };
  }
};
