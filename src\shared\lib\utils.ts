import { QueryParams } from "@/shared/types/action-helper.type"
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}


// Converts a plain JS/TS object (including nested objects, arrays, File/Blob, Date, etc.)
// into a FormData instance that can be sent with `fetch` / `XMLHttpRequest`.
//
// Usage:
// const fd = objectToFormData({ name: "John", avatar: file, tags: ["a", "b"], profile: { age: 25 } });
//
// The function is recursive and will flatten nested structures using HTML-like key
// notation (e.g. profile[age]). It ignores `undefined` values and serialises `null`
// as an empty string to keep parity with regular form submissions.
//
// It throws a descriptive error only if an unexpected failure occurs while appending
// to the FormData instance, but otherwise continues processing the rest of the keys
// to ensure maximum resilience.
/**
 * Converts a plain JS/TS object (including nested objects, arrays, File/Blob, Date, etc.)
 * into a FormData instance that can be sent with `fetch` / `XMLHttpRequest`.
 * @param input - The input object to convert to FormData
 * @returns A FormData instance
 * @example
 * const fd = objectToFormData({ name: "John", avatar: file, tags: ["a", "b"], profile: { age: 25 } });
 * 
 * @exceptions
 * It throws a descriptive error only if an unexpected failure occurs while appending
 * to the FormData instance, but otherwise continues processing the rest of the keys
 * to ensure maximum resilience.
 */
export function objectToFormData<T extends Record<string, unknown>>(input: T): FormData {
    const formData = new FormData()

    const append = (value: unknown, key: string): void => {
        // Skip undefined values entirely – they do not exist in traditional form submissions
        if (value === undefined) return

        // Treat null explicitly – stringify to an empty string to match browser behaviour
        if (value === null) {
            formData.append(key, "")
            return
        }

        // Handle primitive types & Date
        if (value instanceof Date) {
            formData.append(key, value.toISOString())
            return
        }
        if (typeof value === "boolean" || typeof value === "number" || typeof value === "string") {
            formData.append(key, value.toString())
            return
        }

        // Handle File / Blob
        if (value instanceof File || value instanceof Blob) {
            const fileName = (value instanceof File && value.name) ? value.name : "blob"
            formData.append(key, value, fileName)
            return
        }

        // Handle arrays (tuples included)
        if (Array.isArray(value)) {
            value.forEach((v, index) => {
                // Use key[index] notation
                append(v, `${key}[${index}]`)
            })
            return
        }

        // Handle nested objects (but not null, array, Blob, etc.)
        if (typeof value === "object") {
            Object.entries(value as Record<string, unknown>).forEach(([k, v]) => {
                append(v, `${key}[${k}]`)
            })
            return
        }

        // Fallback – if we reached here, we have an unsupported type
        throw new TypeError(`Unsupported data type for FormData conversion at key: ${key}`)
    }

    try {
        Object.entries(input ?? {}).forEach(([key, value]) => {
            append(value, key)
        })
    } catch (error) {
        // Re-throw with additional context so callers can decide how to handle/bubble it
        throw new Error(`Failed to convert object to FormData: ${(error as Error).message}`)
    }

    return formData
}

/**
 * Builds a URL with query parameters from an object
 * @param url - The base URL to append query parameters to
 * @param params - An object containing query parameters and their values
 * @returns A URL with query parameters appended
 * @example
 * const url = buildUrl("https://api.example.com/users", { page: 1, limit: 10, isActive: true, name: undefined, age: null })
 * // Returns "https://api.example.com/users?page=1&limit=10&isActive=true"
 * 
 * @exceptions
 * It throws a descriptive error only if an unexpected failure occurs while appending
 * to the URL, but otherwise continues processing the rest of the keys
 * to ensure maximum resilience.
 */
export function buildUrl(url: string, params: QueryParams) {
    const urlObj = new URL(url)
    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            urlObj.searchParams.set(key, value.toString())
        }
    })
    return urlObj.toString()
}


/**
 * Function to shorten a name to initials,
 * e.g. "John Doe" -> "JD",
 * "John" -> "JO",
 */
export function shortenName(firstName?: string, lastName?: string) {
    if (!firstName) {
        return ""
    }
    if (!lastName) {
        const splittedFirstName = firstName.split(" ")
        if (splittedFirstName.length > 1) {
            const firstInitial = splittedFirstName[0].slice(0, 1).toUpperCase()
            const lastInitial = splittedFirstName[1].slice(0, 1).toUpperCase()
            return `${firstInitial}${lastInitial}`
        }
        return firstName.slice(0, 2).toUpperCase()
    }
    return `${firstName.slice(0, 1)}${lastName.slice(0, 1)}`.toUpperCase()
}


/**
 * Sleep for a given number of milliseconds
 * @param ms - The number of milliseconds to sleep
 * @returns A promise that resolves after the given number of milliseconds
 */
export async function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms))
}