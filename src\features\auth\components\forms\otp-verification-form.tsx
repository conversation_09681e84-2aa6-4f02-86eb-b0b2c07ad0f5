"use client"

import React, { useEffect } from 'react'
import { Form<PERSON>rovider, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { 
    OtpVerificationFormSchema, 
    otpVerificationFormSchema 
} from '@/auth/schemas/forms/registration-form.schema'
import OtpVerificationInput from '@/components/form-inputs/otp-verification-input'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { handleApiResponse } from '@/lib/helpers/client.helpers'
import ResendButton, { ResendButtonProps } from '../resend-button'
import { getUserTempEmail } from '@/lib/helpers/local-storage.helpers'
import { ApiResponse } from '@/shared/types/utility.type'

export const OTP_TIMER_SECONDS = 60

export interface OtpVerificationFormProps {
    /**
     * Server action to verify the OTP
     */
    verifyOtpFn: (data: OtpVerificationFormSchema) => Promise<ApiResponse>,

    /**
     * Redirect URL after successful OTP verification
     */
    successRedirectUrl: string,

    /**
     * Resend button props
     */
    resendButtonProps: ResendButtonProps,
}

/**
 * This form is used to verify the OTP sent to the user's email address
 * It handles the form validation and submission.
 * It also handles the resend OTP functionality.
 * It also handles the timer for the resend OTP functionality.
 */
function OtpVerificationForm({ 
    verifyOtpFn, 
    successRedirectUrl,
    resendButtonProps,
}: OtpVerificationFormProps) {
    const router = useRouter()

    const form = useForm<OtpVerificationFormSchema>({
        resolver: zodResolver(otpVerificationFormSchema),
        defaultValues: {
            otp: '',
            email: '',
        },
    })

    const { isSubmitting, isValid: isFormValid } = form.formState

    // Handles the OTP form submission
    const onSubmit = async (data: OtpVerificationFormSchema) => {
        const res = await verifyOtpFn(data)
        handleApiResponse({
            response: res,
            successMessage: "OTP verified successfully",
            onSuccess: () => {
                router.push(successRedirectUrl)
            },
        })  
    }
    

    // This is used to get the email from the local storage,
    // if the email is not found, then redirect to the register page,
    // else set the email to the email field in the form
    useEffect(() => {
        const email = getUserTempEmail()
        if (email) {
            form.setValue('email', email)
        } else {
            router.push(PAGE_ROUTES.login)
        }
    }, [])

    return (
        <FormProvider {...form}>
            <form 
                onSubmit={form.handleSubmit(onSubmit)} 
                className="w-full flex items-center flex-col gap-7"
            >
                <OtpVerificationInput 
                    control={form.control}
                    name="otp"
                />
                <div className='w-full flex flex-col gap-1 items-center'>
                    <Button
                        className='w-full mt-1 max-w-[400px]'
                        type="submit"
                        disabled={!isFormValid}
                        isLoading={isSubmitting}
                    >
                        Verify
                    </Button>
                    <ResendButton {...resendButtonProps} />
                </div>
            </form>
        </FormProvider>
    )
}

export default OtpVerificationForm