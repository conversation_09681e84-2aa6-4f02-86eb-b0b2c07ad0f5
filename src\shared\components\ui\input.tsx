import * as React from "react"

import { cn } from "@/shared/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"
import { LucideIcon, User } from "lucide-react"
import { Icon } from "@/shared/types"

export const className = `border border-input rounded-md shadow-xs outline-none w-full bg-transparent scroll-mt-5 transition-all ring-ring/50 focus-visible:ring-[3px] focus:ring-[3px] aria-invalid:ring-destructive/30 aria-invalid:bg-destructive/10 disabled:bg-muted disabled:text-muted-foreground`

const iconClassName = "outline-none focus-visible:ring-2 ring-primary/50 focus-visible:ring-offset-2 rounded absolute text-muted-foreground/70 size-5 ring-offset-background"

const inputVariants = cva(
    className,
    {
        variants: {
            variant: {
                default: "",
                destructive:
                    "border-destructive text-destructive placeholder:text-destructive/70 focus-visible:ring-destructive/50 focus-visible:border-destructive",
            },
            size: {
                default: "h-11 px-3 py-1 text-base md:text-sm",
                sm: "h-9 px-2.5 text-sm rounded-md",
                lg: "h-10 px-4 py-2 text-base",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
)

type InputVariantProps = VariantProps<typeof inputVariants>

export type InputProps = Omit<React.ComponentProps<"input">, keyof InputVariantProps> & InputVariantProps & {
    RightIcon?: Icon,
    LeftIcon?: Icon,
    leftIconProps?: React.SVGProps<SVGSVGElement>,
    rightIconProps?: React.SVGProps<SVGSVGElement>,
    /**
     * The class name for the input wrapper
     */
    inputWrapperClassName?: string,
}

function Input({
    className,
    type,
    variant,
    size,
    inputWrapperClassName,
    RightIcon,
    LeftIcon,
    leftIconProps,
    rightIconProps,
    ...props
}: InputProps) {
    return (
        <div className={cn("relative flex items-center w-full", inputWrapperClassName)}>
            {LeftIcon && (
                <LeftIcon
                    {...leftIconProps}
                    className={cn(
                        iconClassName,
                        "left-3", 
                        leftIconProps?.className
                    )}
                />
            )}
            <input
                type={type}
                data-slot="input"
                className={cn(
                    inputVariants({
                        variant,
                        size,
                    }),
                    {
                        "pl-10": LeftIcon,
                        "pr-10": RightIcon,
                    },
                    className,
                )}
                {...props}
            />
            {RightIcon && (
                <RightIcon
                    {...rightIconProps}
                    className={cn(
                        iconClassName,
                        "right-3",
                        rightIconProps?.className
                    )}
                    tabIndex={0}
                />
            )}
        </div>
    )
}


export { Input, inputVariants }
