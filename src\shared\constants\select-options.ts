import { SelectOptions } from "@/shared/types"



/**
 * Options for the gender
 */
export const GENDER_OPTIONS = [
    {
        label: 'Male',
        value: 'Male',
    },
    {
        label: 'Female',
        value: 'Female',
    },
] as const satisfies SelectOptions


/**
 * Options for the yes/no
 */
export const YES_NO_OPTIONS = [
    {
        label: "Yes",
        value: "yes",
    },
    {
        label: "No",
        value: "no"
    }
]

/**
 * Options for the confirmation
 */
export const CONFIRMATION_OPTIONS = [
    {
        label: "I accept the Terms and Conditions",
        value: "termsAndConditions",
    },
    {
        label: "I confirm all details provided are true and accurate",
        value: "confirmDetails",
    },
    {
        label: "I consent to receive communications",
        value: "consentToCommunications",
    },
] as const satisfies SelectOptions


/**
 * Options for the property statuses
 */
export const PROPERTY_STATUSES_OPTIONS = [
    { value: "all", label: "All Statuses" },
    { value: "available", label: "Available" },
    { value: "sold", label: "Sold" },
    { value: "rented", label: "Rented" },
    { value: "unpublished", label: "Unpublished" },
    { value: "draft", label: "Drafts" },

] as const satisfies SelectOptions

export const PROPERTY_TYPES_OPTIONS = [
    { value: "all", label: "All Types" },
    { value: "apartment", label: "Apartment" },
    { value: "villa", label: "Villa" },
    { value: "townhouse", label: "Townhouse" },
    { value: "house", label: "House" },
] as const satisfies SelectOptions