import * as React from "react"
import NextImage, { ImageProps } from "next/image"

import { cn } from "@/lib/utils"

/**
 * Wrapper around `next/image` that injects a default className and keeps the
 * API surface identical. Use this everywhere instead of directly importing
 * `next/image` so that styles and future behaviour tweaks stay consistent.
 */
function Image({ className, ...props }: ImageProps) {
  return <NextImage className={cn("object-cover", className)} {...props} />
}

export { Image } 