"use client"

import React from 'react'
import { Step4FormType, step4Schema } from '@/profile/schemas/profile-registration/individual.schema'
import useIndividualRegistrationFormStore from '@/context/individual-registration-form-store'
import { useFormSteps } from '@/context/form-steps-context'
import NavigationButton from '@/profile/components/verify-profile-form/individual/navigation-button'
import { parseIndustryAndRole, parseIndustryOption } from '@/profile/helpers/options.helpers'
import LicenseInput from '@/profile/components/verify-profile-form/license-input'
import { FormProvider, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { LicenseSchema } from '@/profile/schemas/license.schema'


function StepFourForm() {
    const { updateStep, form: store } = useIndividualRegistrationFormStore()
    const { goToNextStep } = useFormSteps()

    const form = useForm<Step4FormType>({
        resolver: zodResolver(step4Schema),
        defaultValues: store.step4,
    })

    const step3 = store.step3
    const defaultIndustryOtherRoles = step3.defaultIndustryOtherRoles
    const otherIndustriesRoles = step3.otherIndustryRoles
    const defaultIndustryRoles = step3.defaultIndustryRoles

    const onSubmit = (data: Step4FormType) => {
        const filteredDefaultIndustries = Object.entries(data.defaultIndustryRoles || {})
            .reduce((acc, [industryAndRoleId, license]) => {
                // TODO: this splitting __ is not good, make this robust
                const [industry, _] = industryAndRoleId.split("__")
                if (defaultIndustryRoles[industry] === "other") {
                    return acc
                }
                return {
                    ...acc,
                    [industryAndRoleId]: license
                }
            }, {} as Record<string, LicenseSchema>)

        updateStep('step4', {
            ...data,
            defaultIndustryRoles: filteredDefaultIndustries,
        })
        goToNextStep()
    }

    return (
        <FormProvider {...form}>
            <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='flex flex-col gap-8'
            >
                {Object.entries(defaultIndustryRoles || {})
                    .filter(([_, role]) => role !== "other")
                    .map(([industry, role]) => {
                        const fieldName = `${industry}__${role}`
                        const { industry: parsedIndustry, role: parsedRole } = parseIndustryAndRole(fieldName)
                        return (
                            <LicenseInput
                                key={`${industry}-${role}-license-card`}
                                fieldName={`defaultIndustryRoles.${fieldName}`}
                                industryName={parsedIndustry.name}
                                roleName={parsedRole.name}
                            />
                        )
                    })
                }
                {Object.entries(defaultIndustryOtherRoles || {}).map(([industry, role]) => {
                    const fieldName = `${industry}__${role}`
                    const parsedIndustry = parseIndustryOption(industry)
                    return (
                        <LicenseInput
                            key={`${industry}-${role}-license-card`}
                            fieldName={`defaultIndustryOtherRoles.${fieldName}`}
                            industryName={parsedIndustry.name}
                            roleName={role}
                        />
                    )
                })}
                {Object.entries(otherIndustriesRoles || {}).map(([industry, role]) => {
                    return (
                        <LicenseInput
                            key={`${industry}-${role}-license-card`}
                            fieldName={`otherIndustryRoles.${industry}__${role}`}
                            industryName={industry}
                            roleName={role}
                        />
                    )
                })}
                <NavigationButton />
            </form>
        </FormProvider>
    )
}

export default StepFourForm 