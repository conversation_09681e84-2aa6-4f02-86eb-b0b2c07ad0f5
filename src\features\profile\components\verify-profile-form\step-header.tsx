import React from 'react'
import StepBackButton from './step-back-button'

export interface StepHeaderProps {
    title: string
    description?: string
}

function StepHeader({ title, description }: StepHeaderProps) {
    return (
        <div className="text-center flex items-center gap-1 relative justify-center md:items-start">
            <StepBackButton />
            <div className="flex flex-col gap-1 mx-12">
                <h2 className="text-xl font-semibold font-heading md:text-2xl">{title}</h2>
                {description && <p className="text-muted-foreground text-sm">{description}</p>}
            </div>
        </div>
    )
}

export default StepHeader