"use client";
import * as React from "react";
import { Props, tabs } from "./helpers";

export default function FiltersButton({ activeTab, setActiveTab }: Props) {
  return (
    <div className="flex flex-col items-center justify-center py-6">
      <div className="relative flex items-end">
        <div className="mb-px h-px w-40 bg-gray-300" />
        <div className="flex gap-4">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`relative border border-gray-300 px-6 py-3 text-sm font-medium transition-all duration-300
                ${
                  isActive
                    ? "z-10 -mb-px border-b-white bg-white text-black"
                    : "cursor-pointer bg-gray-50 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                }`}
              >
                {tab.label}
              </button>
            );
          })}
        </div>
        <div className="mb-px h-px w-40 bg-gray-300" />
      </div>
    </div>
  );
}
