"use client";
import React from "react";
import { Check, X } from "lucide-react";
import { PackageFeature } from "./data";
import { Badge } from "@/shared/components/ui/badge";

interface PackageCardProps {
  name: string;
  price: string;
  badge: string;
  isActive: boolean;
  features: PackageFeature[];
  buttonText: string;
  colorTheme: string;
  currency: string;
  discount?: {
    id: number;
    name: string;
    type: string;
    value: number;
    createdAt: string;
  };
  onSubscribe: () => void;
}

const PackageCard = ({
  name,
  price,
  badge,
  isActive,
  features,
  buttonText,
  onSubscribe,
  colorTheme,
  currency,
  discount,
}: PackageCardProps) => {
  // Use the "Badge" feature’s text for the centered pill (like “Verified & Pro”)
  const badgeFeature = features?.find(
    (f) => f.featureConstant.toLowerCase().trim() === "badge"
  );
  const badgeText =
    typeof badgeFeature?.featureValue === "string"
      ? badgeFeature.featureValue
      : typeof badge === "string"
      ? badge
      : "";

  return (
    <div
      className={[
        "relative overflow-hidden rounded-2xl bg-white shadow-sm border",
        isActive
          ? "border-2 border-emerald-400 shadow-[0_0_0_2px_rgba(16,185,129,0.2)]"
          : "border-gray-200",
      ].join(" ")}
    >
      {/* Top-left “Active”/badge chip */}
      {isActive && (
        <div className="absolute left-3 top-3 z-10">
          <Badge
            variant="default"
            className="px-3 font-inter text-xs text-white"
            style={{ backgroundColor: isActive ? "#10B981" : "#d1fae5" }}
          >
            Active
          </Badge>
        </div>
      )}
      {/* Top-right check when active */}
      {isActive && (
        <div className="absolute right-3 top-3 z-10 rounded-full border border-emerald-300 bg-white/80 p-1">
          <Check className="h-4 w-4 text-emerald-500" />
        </div>
      )}

      {/* Colored header with name + price */}
      <div
        style={{ backgroundColor: colorTheme }}
        className="p-5 text-center text-white"
      >
        <h2 className="font-inter text-xl font-semibold">{name}</h2>

        <div className="mt-1 text-center font-inter">
          {discount ? (
            <>
              <div className="text-sm text-white/70 line-through">
                {price} {currency || "AED"}
              </div>
              <div className="text-2xl font-bold">
                {discount.type === "percentage"
                  ? `${(
                      Number(price) -
                      (Number(price) * discount.value) / 100
                    ).toFixed(2)}`
                  : `${(Number(price) - discount.value).toFixed(2)}`}{" "}
                {currency || "AED"}
              </div>
            </>
          ) : (
            <div className="text-2xl font-bold">
              {price} {currency || "AED"}
            </div>
          )}
        </div>

        {/* Center pill “Verified & …” */}
        <div className="mt-4">
          <div className="mx-auto w-full max-w-[260px] rounded-full bg-white/20 px-4 py-1 text-sm font-medium">
            {badgeText || "\u00A0"}
          </div>
        </div>
      </div>

      {/* Feature rows */}
      <div className="p-6">
        <div className="space-y-3">
          {features?.map((feature, idx) => (
            <div
              key={`${feature.featureName}-${idx}`}
              className="flex items-center justify-between border-b border-gray-100 pb-2"
            >
              <span className="font-inter text-sm text-[#2C3345]">
                {feature.featureName}
              </span>

              {typeof feature.featureValue === "boolean" ? (
                feature.featureValue ? (
                  <Check className="h-5 w-5 text-[#10B981]" />
                ) : (
                  <X className="h-5 w-5 text-[#F44336]" />
                )
              ) : (
                <span
                  className={[
                    "font-inter text-sm font-medium",
                    feature.featureName === "Risk of Being Blacklisted"
                      ? feature.featureValue === "High"
                        ? "text-[#F44336]"
                        : "text-[#10B981]"
                      : "text-[#2C3345]",
                  ].join(" ")}
                >
                  {feature.featureValue}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* Action button */}
        <button
          className={[
            "mt-6 w-full rounded-md px-4 py-2.5 font-inter text-sm font-medium text-white transition-colors duration-200",
            isActive ? "cursor-not-allowed bg-gray-400" : "hover:opacity-90 cursor-pointer",
          ].join(" ")}
          style={{ backgroundColor: isActive ? "#9CA3AF" : colorTheme }}
          onClick={() => !isActive && onSubscribe()}
          disabled={isActive}
        >
          {buttonText}
        </button>
      </div>
    </div>
  );
};

export default PackageCard;
