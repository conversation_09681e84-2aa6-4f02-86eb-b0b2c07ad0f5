// ----------------------------------------------------------------------------
// Profile type definitions
// ----------------------------------------------------------------------------
// This file defines the strongly-typed shape returned by the “profile details”
// API endpoint.  Each nested object has been broken out into its own interface
// to make the model clearer and easier to maintain.
// ----------------------------------------------------------------------------

// ─────────────────────────────────────────────────────────────────────────────
// Temp-data forms (embedded inside User.tempData)
// ─────────────────────────────────────────────────────────────────────────────
export interface Form1 {
  gender: string | null;
  firstName: string | null;
  middleName: string | null;
  lastName: string | null;
  nationality: string | null;
  phoneNumber: string | null;
  location: number | null;
  professionalPhoto: string | null;
}

export interface Form2 {
  workStatus: string | null;
}

export interface Form3 {
  company: string;
  companyName: string;
  companyEmail: string;
  companyPhone: string;
  expiryDate: string;
  employmentProof: unknown[];
  hasFreelancerPermit: "yes" | "no" | string;
  employmentProofExpiry: string;
  freelancerLicenseFile: unknown[];
  freelancerLicenseNumber: string;
  freelancerLicenseAuthority: string;
  freelancerLicenseAuthorityOther: string;
}

export interface Form4 {
  specialization: string[];
  primaryIndustry: number[];
  primaryIndustry2: string;
  primaryIndustry3: string;
  specializationOther: string;
  primaryIndustryOther: string;
}

export interface Form5 {
  agentType: string[];
  agentTypeOther: string;
  otherAgentTypes: string[];
}

export interface Form6 {
  hasLicense: "yes" | "no" | string;
  licenseFile: unknown[];
  licenseNumber: string;
  licenseAuthority: string;
  licenseexpiryDate: string | null;
  licenseAuthorityOther: string;
}

export interface Form7 {
  visa: Record<string, unknown>[];
  passport: Record<string, unknown>[];
  emiratesId: Record<string, unknown>[];
  referralId: string;
  termsAgree: boolean;
  visaExpiry: string | null;
  passportExpiry: string | null;
  accuracyConfirm: boolean;
  emiratesIdExpiry: string | null;
  communicationConsent: boolean;
}

export interface Form8Item {
  id: string;
  roletype: string;
  hasLicense: "yes" | "no" | null;
  licenseFile: unknown[];
  licenseNumber: string;
  licenseAuthority: string;
  licenseexpiryDate: string | null;
  licenseAuthorityOther: string;
}

export interface TempData {
  form1: Form1;
  form2: Form2;
  form3: Form3;
  form4: Form4;
  form5: Form5;
  form6: Form6;
  form7: Form7;
  form8: Form8Item[];
  formStatus: string;
  requiredFields: string[];
}

// ─────────────────────────────────────────────────────────────────────────────
// Core user object
// ─────────────────────────────────────────────────────────────────────────────
export interface User {
  profileId: number;
  profileStatusId: number | null;
  accountRole: string;
  id: number;
  code: string | null;
  firstName: string;
  middleName: string | null;
  lastName: string;
  localName: string | null;
  emiratesId: string | null;
  nationalityId: number | null;
  locationId: number | null;
  address: string | null;
  email: string;
  phone: string;
  typeId: number | null;
  statusId: number | null;
  createdBy: number | null;
  createdOn: string;
  modifiedBy: number | null;
  modifiedOn: string | null;
  designation: string | null;
  shortDescription: string | null;
  description: string | null;
  specialization: string | null;
  experience: string | null;
  languages: string | null;
  industry: string | null;
  certified: boolean;
  certificateNumber: string | null;
  expiryDate: string | null;
  contactNumber: string | null;
  whatsappContact: string | null;
  contactEmail: string | null;
  cardHolderName: string | null;
  cardType: string | null;
  cardNumber: string | null;
  accountType: string;
  association: boolean;
  issuedBy: string | null;
  profileImage: string | null;
  isProfileCompleted: boolean;
  rejectionReason: string | null;
  tempData: TempData;
  requiredFields: string[] | null;
  isLicensed: boolean;
  licenseTag: string | null;
  customerId: number | null;
  loginId: number;
  loginStatusId: number | null;
  username: string;
  lastLogin: string;
  loginCount: number;
  isActivated: boolean;
  otp: number | null;
  expireOn: string | null;
  accountId: number | null;
  passwordResetAt: string | null;
  roleId: number;
  loginrolestatusid: number | null;
  location: string | null;
  profileStatus: string;
  subscription: unknown | null;
}

// ─────────────────────────────────────────────────────────────────────────────
// Agent-specific structures
// ─────────────────────────────────────────────────────────────────────────────
export interface AgentDetails {
  id: number;
  agent_id: number | null;
  profile_id: number;
  industryMission: string | null;
  operationArea: string | null;
  industrySubCategory: string | null;
  specializationMission: string | null;
  yearOfExperience: string | null;
  otherForFreelancer: string | null;
  employerName: string | null;
  position: string | null;
  employedLocation: string | null;
  nationality: string | null;
  summary: string | null;
  workType: string | null;
  profilePhotos: string | null;
  licenseDocs: string | null;
  freelancePermitDocs: string | null;
  tradeLicenseDocs: string | null;
  employmentLetters: string | null;
  certifications: string | null;
  languages: string | null;
  areaCovered: string | null;
  created_at: string | null;
  updated_at: string | null;
  industryMissionOther: string | null;
  industrySubCategoryOther: string | null;
  workTypeCategory: string | null;
  servicesOffered: string | null;
  gender: string | null;
  personalWebsite: string | null;
  facebook: string | null;
  instagram: string | null;
  linkedin: string | null;
  twitter: string | null;
  youtube: string | null;
  personalIdDoc: string | null;
  companyEmail: string | null;
  companyPhone: string | null;
  hasFreelancerPermit: boolean;
  freelancerLicenseNumber: string | null;
  freelancerLicenseNumberExpiryDate: string | null;
  freelancerLicenseAuthority: string | null;
  freelancerLicenseAuthorityOther: string | null;
  agentRole: string | null;
  hasLicense: boolean;
  termsAgree: boolean;
  accuracyConfirm: boolean;
  communicationConsent: boolean;
  licenseAuthority: string | null;
  employmentProof: string | null;
  emiratesId: string[] | null;
  visa: string[] | null;
  passport: string[] | null;
  positionOther: string | null;
  employmentProofExpiry: string | null;
  emiratesIdExpiry: string | null;
  visaExpiry: string | null;
  passportExpiry: string | null;
  personalIdDocExpiry: string | null;
  passportDocExpiry: string | null;
  visaDocExpiry: string | null;
  supportingDocsDocExpiry: string | null;
}

export interface AgentLicense {
  id: number;
  roleId: number;
  agentId: number;
  licenseFile: string | null;
  licenseexpiryDate: string | null;
  licenseAuthorityOther: string | null;
  roletype: string;
  licenseAuthority: string | null;
  licenseNumber: string | null;
  hasLicense: boolean;
  timestamp: string;
  agencyId: number | null;
  licenseIssueDate: string | null;
}

/**
 * The profile details object type from the API response
 */
export interface ApiProfileDetails {
  user: User;
  agent: unknown | null;
  account: unknown | null;
  agentDetails: AgentDetails;
  operationAreas: unknown | null;
  industryMissionNames: string[];
  industrySubMissionNames: string[];
  agentlicenses: AgentLicense[];
  compayDetails: unknown | null; // (note: key spelling kept to match API)
  companyrole: unknown[];
}
