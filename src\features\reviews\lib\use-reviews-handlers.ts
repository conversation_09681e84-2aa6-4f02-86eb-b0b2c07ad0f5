'use client';

import { useState, useMemo, useEffect } from 'react';
import { TReview, ReviewStats } from '../types/review.type';
import {
  updateReviewStatus,
  flagReview,
  hideReview,
  publishReview,
  deleteReview,
  addNoteToReview,
} from '../actions/reviews.action';
import { errorToast, successToast } from '@/shared/lib/toast';

export const useReviewsHandlers = (initialReviews: TReview[]) => {
  const [reviews, setReviews] = useState<TReview[]>(initialReviews);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedReview, setSelectedReview] = useState<TReview | null>(null);
  const [moderationResponse, setModerationResponse] = useState('');

  // Sync internal reviews state with prop changes
  useEffect(() => {
    setReviews(initialReviews);
  }, [initialReviews]);

  // Filter reviews based on search and filters
  const filteredReviews = useMemo(() => {
    return reviews.filter((review) => {
      const matchesSearch = searchTerm === '' || 
        review.reviewerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.reviewerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.targetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        review.comment.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || review.status === statusFilter;
      const matchesType = typeFilter === 'all' || review.targetType === typeFilter;

      return matchesSearch && matchesStatus && matchesType;
    });
  }, [reviews, searchTerm, statusFilter, typeFilter]);

  // Calculate stats
  const stats: ReviewStats = useMemo(() => {
    return {
      total: reviews.length,
      published: reviews.filter(r => r.status === 'published').length,
      pending: reviews.filter(r => r.status === 'pending').length,
      flagged: reviews.filter(r => r.status === 'flagged').length,
      hidden: reviews.filter(r => r.status === 'hidden').length,
      averageRating: reviews.length > 0 
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
        : 0,
      replied: reviews.filter(r => r.comments && r.comments.length > 0).length,
    };
  }, [reviews]);

  const handleStatusChange = async (id: string, status: TReview['status']) => {
    try {
      const result = await updateReviewStatus(id, status);
      if (result.success) {
        setReviews(prev => prev.map(review => 
          review.id === id ? { ...review, status, updatedAt: new Date().toISOString() } : review
        ));
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to update review status");
    }
  };

  const handleFlag = async (id: string, reason: string) => {
    try {
      const result = await flagReview(id, reason);
      if (result.success) {
        setReviews(prev => prev.map(review => 
          review.id === id ? { 
            ...review, 
            status: 'flagged',
            moderationRequest: {
              reason,
              requestedBy: 'admin',
              requestDate: new Date().toISOString(),
            },
            updatedAt: new Date().toISOString()
          } : review
        ));
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to flag review");
    }
  };

  const handleHide = async (id: string, reason: string) => {
    try {
      const result = await hideReview(id, reason);
      if (result.success) {
        setReviews(prev => prev.map(review => 
          review.id === id ? { 
            ...review, 
            status: 'hidden',
            hiddenReason: reason,
            updatedAt: new Date().toISOString()
          } : review
        ));
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to hide review");
    }
  };

  const handleRestore = async (id: string) => {
    try {
      const result = await publishReview(id);
      if (result.success) {
        setReviews(prev => prev.map(review => 
          review.id === id ? { 
            ...review, 
            status: 'published',
            hiddenReason: undefined,
            updatedAt: new Date().toISOString()
          } : review
        ));
        successToast("Review has been restored and published");
      }
    } catch (error) {
      errorToast("Failed to restore review");
    }
  };

  const handlePublish = async (id: string) => {
    try {
      const result = await publishReview(id);
      if (result.success) {
        setReviews(prev => prev.map(review => 
          review.id === id ? { 
            ...review, 
            status: 'published',
            updatedAt: new Date().toISOString()
          } : review
        ));
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to publish review");
    }
  };

  const handleDeleteReview = async (id: string) => {
    try {
      const result = await deleteReview(id);
      if (result.success) {
        setReviews(prev => prev.filter(review => review.id !== id));
        setSelectedReview(null);
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to delete review");
    }
  };

  const handleAddNote = async (id: string, note: string) => {
    try {
      const result = await addNoteToReview(id, note);
      if (result.success) {
        // Note: In a real implementation, you'd also update the comments array
        successToast(result.message);
      }
    } catch (error) {
      errorToast("Failed to add note");
    }
  };

  const handleModerationResponse = async (id: string, action: 'approve' | 'reject', notes?: string) => {
    if (action === 'approve') {
      await handlePublish(id);
    } else {
      await handleHide(id, notes || 'Rejected during moderation');
    }
  };

  const handleDropdownViewDetails = (id: string) => {
    const review = reviews.find(r => r.id === id);
    if (review) {
      setSelectedReview(review);
    }
  };

  const handleDropdownAddReview = () => {
    // TODO: Implement add review functionality
    successToast("Add review functionality will be implemented soon");
  };

  return {
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    typeFilter,
    setTypeFilter,
    selectedReview,
    setSelectedReview,
    moderationResponse,
    setModerationResponse,
    reviews,
    filteredReviews,
    stats,
    handleAddNote,
    handleDeleteReview,
    handleStatusChange,
    handleFlag,
    handleHide,
    handleRestore,
    handlePublish,
    handleModerationResponse,
    handleDropdownViewDetails,
    handleDropdownAddReview,
  };
};
