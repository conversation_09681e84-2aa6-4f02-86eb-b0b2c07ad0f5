"use client"

import OtpVerificationLayout from '@/components/otp-verification-layout'
import { PAGE_ROUTES } from '@/constants/page-routes'
import { resendOtp, verifyPasswordOtp } from '@/features/auth/actions/auth.action'

function PasswordOtpVerificationPage() {
    return (
        <OtpVerificationLayout
            header="Enter Confirmation Code"
            otpVerificationFormProps={{
                verifyOtpFn: verifyPasswordOtp,
                successRedirectUrl: PAGE_ROUTES.resetPassword,
                resendButtonProps: {
                    resendOtpFn: resendOtp,
                },
            }}
        />
    )
}

export default PasswordOtpVerificationPage