"use server"

import API_ENDPOINTS from "@/constants/api-routes";
import { executeApiRequest } from "@/lib/helpers/actions.helpers";
import { REQUEST_TYPE } from "@/types/action-helper.type";

export async function uploadFile(file: File, keyName: string) {
    const response = await executeApiRequest({
        method: REQUEST_TYPE.post,
        url: API_ENDPOINTS.UPLOAD_FILE,
        isFormData: true,
        body: {
            file,
            keyName,
        },
        withAuth: true,
    })
    return response
}
