import { Button } from "@/shared/components/ui/button";
import { ArrowDown, <PERSON><PERSON><PERSON>, ArrowUpDown } from "lucide-react";

/**
 * -------------------------------------------
 * Utility: A single reusable sortable header
 * -------------------------------------------
 */
export interface SortableHeaderProps {
    column: any;
    label: string;
    align?: "left" | "right";
}
export const SortableHeader: React.FC<SortableHeaderProps> = ({ column, label, align = "left" }) => {
    const sorted = column.getIsSorted();
    return (
        <Button
            variant="none"
            size="none"
            className={`group px-0 py-0 font-semibold hover:bg-transparent ${align === "right" ? "justify-end" : "justify-start"}`}
            onClick={() => column.toggleSorting(sorted === "asc")}
        >
            <span className="mr-1">{label}</span>
            {sorted === "asc" && <ArrowUp className="h-4 w-4 opacity-70 transition-opacity group-hover:opacity-100" />}
            {sorted === "desc" && <ArrowDown className="h-4 w-4 opacity-70 transition-opacity group-hover:opacity-100" />}
            {sorted === false && <ArrowUpDown className="h-4 w-4 opacity-40 group-hover:opacity-70" />}
        </Button>
    );
};