import PageHeading from '@/components/page-heading'
import { PropertyListingTable } from '@/properties/components/properties-listing-table'
import StatsCard from '@/components/stats-card'
import SurfaceCard from '@/components/surface-card'
import { Button } from '@/components/ui/button'
import React from 'react'
import { 
    Building2, 
    CheckCircle, 
    CircleDollarSign, 
    Clock1, 
    Download, 
    File, 
    PlusIcon,
} from 'lucide-react'

function PropertyPage() {
    return (
        <div className='flex flex-col gap-5'>
            <div className="flex flex-col w-full gap-4 md:flex-row md:justify-between">
                <PageHeading
                    title='Properties'
                    description='Manage your property listings and drafts'
                />
                <div className='grid grid-cols-2 gap-3 md:grid-cols-2'>
                    <Button variant={'outline'}>
                        <Download />
                        Import Property
                    </Button>
                    <Button variant={"outline-primary"}>
                        <PlusIcon />
                        List Property
                    </Button>
                </div>
            </div>
            <div className="grid grid-cols-2 gap-3 md:grid-cols-2 xl:grid-cols-5 xl:gap-5">
                <StatsCard 
                    title='Total'
                    value='16/100'
                    Icon={Building2}
                    className='bg-linear-to-br from-primary/5 to-primary/10 text-primary'
                />
                <StatsCard 
                    title='Available'
                    value='8'
                    Icon={CheckCircle}
                    className='bg-linear-to-br from-success/5 to-success/10 text-success'
                />
                <StatsCard 
                    title='Sold'
                    value='1'
                    Icon={CircleDollarSign}
                    className='bg-linear-to-br from-destructive/5 to-destructive/10 text-destructive'
                />
                <StatsCard 
                    title='Rented'
                    value='3'
                    Icon={Clock1}
                    className='bg-linear-to-br from-warning/5 to-warning/10 text-warning'
                />
                <StatsCard 
                    title='Drafts'
                    value='2'
                    Icon={File}
                    className='bg-linear-to-br from-muted/50 to-white text-muted-foreground'
                />
            </div>
            <SurfaceCard
                heading='Properties Listings'
                description='View and manage all your property listings including drafts'
            >
                <PropertyListingTable />
            </SurfaceCard>
        </div>
    )
}

export default PropertyPage