"use client";

import * as React from "react";
import {
    ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    SortingState,
    useReactTable,
    VisibilityState,
} from "@tanstack/react-table";
import {
    Search,
} from "lucide-react";

import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { FormProvider, useForm } from "react-hook-form";
import { listings, PropertyListing } from "./data";
import { columnsHeader, useColumns } from "./columns";
import { cn } from "@/shared/lib/utils";
import FiltersButton from "./filters-button";



/**
 * -------------------------------------------
 * Property Listing Table Component
 * -------------------------------------------
 */
export function PropertyListingTable() {
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
    const columns = useColumns()
    // const [rowSelection, setRowSelection] = React.useState({});

    const table = useReactTable({
        data: listings,
        columns,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        // onRowSelectionChange: setRowSelection,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            // rowSelection,
        },
    });

    const form = useForm()

    return (
        <FormProvider {...form}>
            <div className="w-full space-y-4">
                {/* Filters */}
                <div className="flex gap-2 md:items-center">
                    <Input
                        placeholder="Search property"
                        value={(table.getColumn("property")?.getFilterValue() as string) ?? ""}
                        onChange={(event) =>
                            table.getColumn("property")?.setFilterValue(event.target.value)
                        }
                        inputWrapperClassName="md:max-w-sm"
                        LeftIcon={Search}
                        size={"sm"}
                    />
                    <FiltersButton />
                </div>

                {/* Table */}
                <div className="overflow-x-auto grid rounded-md md:pt-0 md:border">
                    <Table>
                        <TableHeader className="bg-muted/40 hidden md:table-header-group">
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow key={headerGroup.id}>
                                    {headerGroup.headers.map((header) => (
                                        <TableHead 
                                            key={header.id} 
                                            className="whitespace-nowrap px-3 py-4 text-sm font-semibold"
                                        >
                                            {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody className="[&_tr:last-child]:border md:[&_tr:last-child]:border-0">
                            {table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row, rowIndex) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={row.getIsSelected() && "selected"}
                                        className="hover:bg-muted/20 relative block border px-3 rounded-md mb-6 md:mb-0 md:p-0 md:table-row md:border-x-0 md:border-t-0 md:border-b"
                                    >
                                        {row.getVisibleCells().map((cell, cellIndex) => {
                                            const label = columnsHeader[cell.column.id as keyof PropertyListing];
                                            return (
                                                <TableCell
                                                    key={cell.id}
                                                    data-table-cell={label}
                                                    className={cn(
                                                        `p-0 py-3 grid grid-cols-[10ch_1fr] font-medium before:content-[attr(data-table-cell)] border-b border-border/50 before:flex before:pt-0.5 before:text-xs before:font-normal md:p-3 md:table-cell md:before:content-none`,
                                                        {
                                                            // Remove border from last cell
                                                            "border-none ": cellIndex === row.getVisibleCells().length - 1
                                                        },
                                                        {
                                                            "grid-cols-1 border-none md:p-3 md:right-auto before:content-none": cell.column.id === "actions"
                                                        }
                                                    )}
                                                >
                                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                                </TableCell>
                                            )
                                        })}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={columns.length} className="h-24 text-center">
                                        No results.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>

                {/* Footer */}
                <div className="flex flex-col items-center justify-between gap-2 sm:flex-row sm:gap-0">
                    {/* <div className="text-muted-foreground text-sm w-full">
                        {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
                    </div> */}
                    {/* <TablePagination /> */}
                </div>
            </div>
        </FormProvider>
    );
}
