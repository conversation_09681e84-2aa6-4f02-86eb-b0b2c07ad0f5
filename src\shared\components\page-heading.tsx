import { cn } from '@/lib/utils'
import React from 'react'

/**
 * <PERSON>ps interface for the PageHeading component.
 * 
 * @interface PageHeadingProps
 * @property title - The main heading text to display (required)
 * @property description - Optional subtitle/description text displayed below the title
 * @property containerClassName - Optional tailwind classes to apply to the outer container div
 * @property titleClassName - Optional tailwind classes to apply to the title h1 element
 * @property descriptionClassName - Optional tailwind classes to apply to the description paragraph element
 */
interface PageHeadingProps  {
    title: string,
    description?: string,
    containerClassName?: string,
    titleClassName?: string,
    descriptionClassName?: string,
}

/**
 * PageHeading Component
 * 
 * A reusable component for displaying consistent page headings throughout the application.
 * Renders a title with an optional description, providing flexible styling options through
 * className props for customization while maintaining design consistency.
 * 
 * @component
 * @param {PageHeadingProps} props - The component props
 * 
 * @example
 * // Basic usage with title only
 * <PageHeading title="Dashboard" />
 * 
 * @example
 * // With title and description
 * <PageHeading 
 *   title="User Profile" 
 *   description="Manage your account settings and preferences" 
 * />
 * 
 * @example
 * // With custom styling
 * <PageHeading 
 *   title="Settings" 
 *   description="Configure your application preferences"
 *   containerClassName="mb-8"
 *   titleClassName="text-3xl text-primary"
 *   descriptionClassName="text-base"
 * />
 */
function PageHeading(props: PageHeadingProps) {
    return (
        <div className={cn('flex flex-col gap-1', props.containerClassName)}>
            {/* Title */}
            <h1 className={cn('text-2xl font-bold font-heading lg:text-3xl', props.titleClassName)}>
                {props.title}
            </h1>
            
            {/* Optional description - only rendered when provided */}
            {props.description && 
                <p className={cn('text-sm text-muted-foreground', props.descriptionClassName)}>
                    {props.description}
                </p>
            }
        </div>
    )
}

export default PageHeading