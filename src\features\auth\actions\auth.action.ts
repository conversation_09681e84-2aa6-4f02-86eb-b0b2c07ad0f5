"use server"

import API_ENDPOINTS from "@/shared/constants/api-routes"
import { ApiResponse } from "@/shared/types/utility.type"
import { REQUEST_TYPE } from "@/shared/types/action-helper.type"
import { executeApiRequest } from "@/lib/helpers/actions.helpers"
import {
    forgotPasswordFormSchema,
    ForgotPasswordFormSchema,
    LoginFormSchema,
    ResetPasswordFormSchema,
    resetPasswordFormSchema
} from "@/auth/schemas/forms/login-form.schema"
import {
    otpVerificationFormSchema,
    OtpVerificationFormSchema,
    registrationFormSchema,
    RegistrationFormSchema,
    resendOtpSchema
} from "@/auth/schemas/forms/registration-form.schema"
import { AUTH_COOKIE_NAME } from "@/constants/cookie-names"
import { fetchClient } from "@/lib/fetch-client"
import { getCookieFromResponse, removeAuthCookie, setCookie } from "@/lib/helpers/cookies.helpers"


/**
 * Registers a new agent (sign-up flow).
 *
 * Uses the shared `executeApiRequest` helper to POST the registration
 * payload to the backend. The data is validated against
 * `registrationFormSchema` prior to the request for an additional layer of
 * safety on top of the client-side validation.
 *
 * @param data - Sanitised registration form data.
 * @returns A typed `ApiResponse` representing success or failure of the request.
 */
export async function registerAgent(data: RegistrationFormSchema): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.REGISTER_AGENT,
        method: REQUEST_TYPE.post,
        body: data,
        schema: registrationFormSchema,
        isFormData: true,
    })
    return response
}


/**
 * Verifies the one-time password (OTP) that was emailed to the user during
 * registration.
 *
 * @param data - Object containing the user's email and the OTP code.
 * @returns A typed `ApiResponse` indicating whether the OTP was accepted.
 */
export async function verifyOtp(data: OtpVerificationFormSchema): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.REGISTER_OTP_VERIFICATION,
        method: REQUEST_TYPE.post,
        body: data,
        schema: otpVerificationFormSchema,
    })
    return response
}


/**
 * Requests a new registration OTP for the given email address.
 *
 * @param email - The email address associated with the pending registration.
 * @returns A typed `ApiResponse` with the outcome of the resend operation.
 */
export async function resendOtp(email: string): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.RESEND_REGISTER_OTP,
        method: REQUEST_TYPE.post,
        body: { email },
        schema: resendOtpSchema,
    })
    return response
}


/**
 * Handles the login flow. It validates the user's credentials and redirects
 * to the dashboard if successful.
 */
export async function loginAgent(data: LoginFormSchema): Promise<ApiResponse> {
    // Fetch the login response
    const res = await fetchClient({
        url: API_ENDPOINTS.LOGIN_AGENT,
        options: {
            method: REQUEST_TYPE.post,
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json",
            },
        }
    })

    // If the login response is not ok, return an error
    if (!res.ok) {
        return {
            success: false,
            message: "Failed to login",
            status: res.status,
        }
    }

    // Get the auth cookie properties from the response
    const cookieProperties = getCookieFromResponse(AUTH_COOKIE_NAME, res)

    // Set the auth cookie in the next cookie to be used in the client
    if (cookieProperties) {
        await setCookie(cookieProperties)
    }

    // Return the login success response
    return {
        success: true,
        message: "Login successful",
        status: res.status,
        data: {
            isLoggedIn: true,
        },
    }
}


/**
 * Handles the forgot password flow. It sends an OTP to the user's email address.
 */
export async function forgotPassword(data: ForgotPasswordFormSchema): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.AGENT_FORGET_PASSWORD,
        method: REQUEST_TYPE.post,
        body: data,
        schema: forgotPasswordFormSchema,
    })
    return response
}


/**
 * Verifies the one-time password (OTP) that was emailed to the user during
 * password reset.
 */
export async function verifyPasswordOtp(data: OtpVerificationFormSchema): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.REGISTER_OTP_VERIFICATION,
        method: REQUEST_TYPE.post,
        body: data,
        schema: otpVerificationFormSchema,
    })
    return response
}


/**
 * This function is used to reset the password of the user
 */
export async function resetPassword(data: ResetPasswordFormSchema): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.AGENT_RESET_PASSWORD,
        method: REQUEST_TYPE.post,
        body: data,
        schema: resetPasswordFormSchema,
    })
    return response
}

/**
 * Logs out the agent by deleting the auth cookie.
 * @returns A typed `ApiResponse` indicating whether the logout was successful.
 */
export async function logoutUser(): Promise<ApiResponse> {
    const response = await executeApiRequest({
        url: API_ENDPOINTS.LOGOUT,
        method: REQUEST_TYPE.get,
        withAuth: true,
    })
    if (response.success) {
        await removeAuthCookie()
    }
    return response
}