import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768
const IPAD_BREAKPOINT = 1024

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}

export function useIsIpad() {
  const [isIpad, setIsIpad] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${IPAD_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsIpad(window.innerWidth < IPAD_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsIpad(window.innerWidth < IPAD_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isIpad
}