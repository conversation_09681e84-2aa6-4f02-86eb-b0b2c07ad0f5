"use client";

export interface PackageFeature {
  featureConstant: string;
  featureName: string;
  displayOrder: number;
  featureType: string;
  featureValue: string | boolean | React.ReactNode;
}

export interface SubscriptionPackage {
  packageId: number;
  name: string;
  price: string;
  badge: string;
  isActive?: boolean;
  subscriptionId?: number;
  features: PackageFeature[];
  buttonText: string;
  currency: string;
  buttonLink: string;
  colorTheme: string;
  interval: "month" | "year";
  discount?: {
    id: number;
    name: string;
    type: string; // "percentage" | "fixed"
    value: number;
    createdAt: string;
  };
}

export async function getPackagesData(apiBase: string) {
  const myHeaders = new Headers();
  const res = await fetch(`${apiBase}/packages`, {
    headers: myHeaders,
    redirect: "follow",
    credentials: "include",
  });
  const result = await res.json();
  return result;
}

export async function subscribeToPackage(apiBase: string, id: number) {
  const formData = new FormData();
  formData.append("packageTypeId", String(id));

  const response = await fetch(`${apiBase}/packages`, {
    method: "POST",
    redirect: "follow",
    credentials: "include",
    body: formData,
  });

  const result = await response.json();
  return { ok: response.ok, result };
}

export async function cancelSubscription(
  apiBase: string,
  subscriptionId: number
) {
  const response = await fetch(`${apiBase}/${subscriptionId}/cancel`, {
    method: "PUT",
    redirect: "follow",
    credentials: "include",
  });

  const result = await response.json();
  return { ok: response.ok, result };
}
