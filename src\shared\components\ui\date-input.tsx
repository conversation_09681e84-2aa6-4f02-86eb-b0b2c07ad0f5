"use client"

import * as React from "react"
import { ComponentProps } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { className as inputClassName } from "@/shared/components/ui/input"
import { cn } from "@/lib/utils"
import { Button } from "@/shared/components/ui/button"
import { Calendar } from "@/shared/components/ui/calendar"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"

export interface DateInputProps {
    date: Date | undefined
    setDate: (date: Date | undefined) => void
    ref: React.RefCallback<HTMLButtonElement>
    ariaInvalid?: boolean
    placeholder?: string
    disabled?: ComponentProps<typeof Calendar>["disabled"]
    fromYear?: number
    toYear?: number
}

export function DateInput({ 
    date, 
    setDate, 
    ref, 
    ariaInvalid, 
    placeholder,
    disabled,
    fromYear,
    toYear
}: DateInputProps) {
    const [open, setOpen] = React.useState(false)
    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    data-empty={!date}
                    className={cn(
                        inputClassName,
                        "data-[empty=true]:text-muted-foreground w-full justify-start text-left h-11 aria-invalid:border-input hover:bg-transparent",
                    )}
                    ref={ref}
                    aria-invalid={ariaInvalid}
                >
                    {date ? format(date, "PPP") : <span>{placeholder}</span>}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
                <Calendar 
                    mode="single" 
                    selected={date} 
                    onSelect={(date) => {
                        setDate(date)
                        setOpen(false)
                    }}
                    captionLayout="dropdown"
                    disabled={disabled}
                    fromYear={fromYear}
                    toYear={toYear}
                />
            </PopoverContent>
        </Popover>
    )
}