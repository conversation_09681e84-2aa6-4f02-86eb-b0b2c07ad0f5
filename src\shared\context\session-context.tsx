"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { getProfileInfo } from "@/features/profile/actions"
import { logoutUser } from "@/features/auth/actions/auth.action"

type Session = {
    id: number
    email: string
    name: string
    role: string
    createdAt?: string
    updatedAt?: string
    profileImage?: string | null
}

type SessionContextType = {
    /**
     * The session data
     */
    session: Session | null
    setSession: (session: Session) => void
    /**
     * True if the session is being fetched,
     * False if the session is fetched
     */
    isLoading: boolean
}

const SessionContext = createContext<SessionContextType | null>(null)

const SessionProvider = ({ children }: { children: React.ReactNode }) => {
    const [session, setSession] = useState<Session | null>(null)
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        const fetchSession = async () => {
            const session = await getProfileInfo()
            if (!session.success) {
                await logoutUser()
                setIsLoading(false)
                return
            }
            setSession({
                id: session.data.user.id,
                email: session.data.user.email,
                name: session.data.user.firstName + " " + session.data.user.lastName,
                role: session.data.user.accountRole,
                profileImage: session.data.user.profileImage,
            })
            setIsLoading(false)
        }
        fetchSession()
    }, [])

    return (
        <SessionContext.Provider
            value={{ session, setSession, isLoading }}
        >
            {children}
        </SessionContext.Provider>)
}

export function useSession() {
    const context = useContext(SessionContext)
    if (!context) {
        throw new Error("useSession must be used within a SessionProvider")
    }
    return context
}

export { SessionContext, SessionProvider }