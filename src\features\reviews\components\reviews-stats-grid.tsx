import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/shared/components/ui/card";

interface Stats {
  total: number;
  published: number;
  pending: number;
  flagged: number;
  hidden: number;
  averageRating: number;
  replied: number;
}

const renderStars = (rating: number) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  
  for (let i = 0; i < 5; i++) {
    if (i < fullStars) {
      stars.push(
        <span key={i} className="text-yellow-400 text-xl">★</span>
      );
    } else if (i === fullStars && hasHalfStar) {
      stars.push(
        <span key={i} className="text-yellow-400 text-xl">☆</span>
      );
    } else {
      stars.push(
        <span key={i} className="text-gray-300 text-xl">☆</span>
      );
    }
  }
  
  return stars;
};

const ReviewsStatsGrid: React.FC<{ stats: Stats }> = ({ stats }) => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <Card className="w-[373px] h-[108px] flex flex-col justify-center">
      <CardHeader className="pb-1">
        <CardTitle className="text-sm font-medium text-gray-600">Total Reviews</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-2xl font-bold">{stats.total}</div>
      </CardContent>
    </Card>
    <Card className="w-[373px] h-[108px] flex flex-col justify-center">
      <CardHeader className="pb-1">
        <CardTitle className="text-sm font-medium text-gray-600">Average Rating</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex items-center gap-2">
          <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</div>
          <div className="flex">{renderStars(Math.round(stats.averageRating))}</div>
        </div>
      </CardContent>
    </Card>
    <Card className="w-[373px] h-[108px] flex flex-col justify-center">
      <CardHeader className="pb-1">
        <CardTitle className="text-sm font-medium text-gray-600">Replied</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-2xl font-bold text-green-600">{stats.replied}</div>
      </CardContent>
    </Card>
    <Card className="w-[373px] h-[108px] flex flex-col justify-center">
      <CardHeader className="pb-1">
        <CardTitle className="text-sm font-medium text-gray-600">Flagged</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="text-2xl font-bold text-orange-600">{stats.flagged}</div>
      </CardContent>
    </Card>
  </div>
);

export default ReviewsStatsGrid;