import { AlertTriangle } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table';
import SurfaceCard from '@/shared/components/surface-card';
import { TReview } from '../types/review.type';
import ReviewStars from './review-stars';
import ReviewStatusBadge from './review-status-badge';
import ReviewActionsDropdown from './review-actions-dropdown';

interface ReviewsTableProps {
  reviews: TReview[];
  filteredReviews: TReview[];
  onStatusChange: (id: string, status: TReview['status']) => void;
  onFlag: (id: string, reason: string) => void;
  onRestore: (id: string) => void;
  onHide: (id: string, reason: string) => void;
  onPublish: (id: string) => void;
  onViewDetails: (id: string) => void;
  onAddReview: () => void;
  onAddNote?: (id: string, note: string) => void;
}

const ReviewsTable = ({
  reviews,
  filteredReviews,
  onStatusChange,
  onFlag,
  onRestore,
  onHide,
  onPublish,
  onViewDetails,
  onAddReview,
  onAddNote,
}: ReviewsTableProps) => {
  return (
    <SurfaceCard>
      <div className="p-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reviewer</TableHead>
              <TableHead>Target</TableHead>
              <TableHead>Rating</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredReviews.map((review) => (
              <TableRow key={review.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{review.reviewerName}</div>
                    <div className="text-sm text-muted-foreground">{review.reviewerEmail}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{review.targetName}</div>
                    <div className="text-sm text-muted-foreground capitalize">{review.targetType}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <ReviewStars rating={review.rating} />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <ReviewStatusBadge 
                      status={review.status}
                      onStatusChange={(newStatus) => onStatusChange(review.id, newStatus as TReview['status'])}
                    />
                    {(review.status === 'flagged' || review.status === 'pending') && (
                      <AlertTriangle className="h-4 w-4 text-yellow-60.00" />
                    )}
                  </div>
                </TableCell>
                <TableCell>{new Date(review.createdAt).toLocaleDateString()}</TableCell>
                <TableCell>
                  <ReviewActionsDropdown
                    review={review}
                    onStatusChange={onStatusChange}
                    onFlag={onFlag}
                    onRestore={onRestore}
                    onHide={onHide}
                    onPublish={onPublish}
                    onViewDetails={onViewDetails}
                    onAddReview={onAddReview}
                    onAddNote={onAddNote}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {filteredReviews.length === 0 && (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No reviews found</h3>
            <p className="text-muted-foreground">Try adjusting your filters or search terms.</p>
          </div>
        )}
      </div>
    </SurfaceCard>
  );
};

export default ReviewsTable;
