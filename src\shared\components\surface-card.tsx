import React, { PropsWithChildren } from 'react'
import { cn } from '@/lib/utils'
import SectionHeading, { SectionHeadingProps } from './section-heading'

/**
 * Props interface for the SurfaceCard component.
 * 
 * @interface SurfaceCardProps
 * @property {React.ReactNode} children - The content to be rendered inside the card
 * @property {string} className - Optional tailwind classes to apply to the outer container div
 */
interface SurfaceCardProps extends PropsWithChildren {
    /**
     * Optional tailwind classes to apply to the outer container div
     */
    className?: string

    heading?: string

    headingProps?: SectionHeadingProps

    description?: string
}

/**
 * SurfaceCard Component
 * 
 * A reusable component for displaying a surface card.
 */
function SurfaceCard({ children, className, heading, headingProps, description }: SurfaceCardProps) {
    return (
        <div className={cn('bg-background-light rounded-lg p-4 shadow-xs flex flex-col gap-5', className)}>
            {heading && <div className="flex flex-col gap-0.5">
                <SectionHeading {...headingProps}>{heading}</SectionHeading>
                {description && <p className='text-xs text-muted-foreground md:text-sm'>{description}</p>}
            </div>}
            {children}
        </div>
    )
}

export default SurfaceCard
